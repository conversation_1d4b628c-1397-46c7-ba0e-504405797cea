# LinkedIn 分享卡片下载功能

## 功能概述

为 LinkedIn 分享卡片组件 (`ShareCardLinkedin`) 添加了下载功能，用户可以将分享卡片保存为 PNG 图片。

## 实现详情

### 1. 依赖库
使用了 `@zumer/snapdom` 库来实现 DOM 到图片的转换：
- 版本：1.9.7
- 特点：快速、准确、支持现代浏览器
- 支持 SVG、PNG、JPG、WebP 等格式

### 2. 新增功能

#### 下载按钮
- 位置：分享卡片右下角，紧邻分享按钮
- 样式：与现有按钮保持一致，支持深色/浅色主题
- 状态：下载中显示加载动画和 "Downloading..." 文字

#### 下载逻辑
```typescript
const handleDownload = async () => {
  // 1. 检查是否正在下载或在服务端
  if (isDownloading.value || typeof window === 'undefined') return

  // 2. 设置下载状态
  isDownloading.value = true

  try {
    // 3. 隐藏操作按钮（确保截图干净）
    const actionButtons = cardElement.querySelector('[data-action-buttons]')
    if (actionButtons) {
      actionButtons.style.display = 'none'
    }

    // 4. 使用 snapdom 生成图片
    const result = await snapdom(cardElement, {
      scale: 2,              // 2倍分辨率
      backgroundColor: isDark ? '#141415' : '#ffffff',
      embedFonts: false,     // 不嵌入字体（提高性能）
      fast: true,           // 快速模式
      compress: true        // 压缩输出
    })

    // 5. 转换为 PNG 并下载
    const blob = await result.toBlob({ type: 'png' })
    downloadFromBlob(blob)

  } finally {
    // 6. 恢复操作按钮显示
    if (actionButtons) {
      actionButtons.style.display = 'flex'
    }
    isDownloading.value = false
  }
}
```

### 3. 文件修改

#### `components/ShareCardLinkedin/index.vue`
- 添加了下载按钮 UI
- 导入 `snapdom` 库
- 添加下载相关的响应式变量和函数
- 修复了图片错误处理的 TypeScript 类型问题

#### 新增的响应式变量
```typescript
const isDownloading = ref(false)
const downloadButtonText = computed(() => 
  isDownloading.value ? 'Downloading...' : 'Download'
)
const downloadButtonClass = computed(() => {
  // 根据主题返回相应的样式类
})
```

### 4. 使用方法

#### 在现有页面中使用
```vue
<ShareCardLinkedin
  :show="showPopup"
  :user="user"
  :linkedin-data="linkedinData"
  :is-dark="isDark"
  @close="showPopup = false"
/>
```

下载按钮会自动显示在卡片的右下角，用户点击即可下载。

#### 测试页面
创建了测试页面 `/test-linkedin-download` 用于验证功能：
- 包含完整的 LinkedIn 分享卡片
- 包含简化的测试组件
- 支持主题切换测试

### 5. 特性

#### 优点
- **高质量输出**：2倍分辨率，确保图片清晰
- **主题支持**：自动适配深色/浅色主题背景
- **用户体验**：下载时隐藏操作按钮，确保截图干净
- **性能优化**：使用快速模式和压缩选项
- **错误处理**：包含完整的错误处理和用户反馈

#### 文件命名
下载的文件自动命名为：`dinq-linkedin-analysis-{timestamp}.png`

#### 兼容性
- 支持现代浏览器
- 自动处理 CORS 图片问题
- 服务端渲染安全（检查 `typeof window === 'undefined'`）

### 6. 技术细节

#### snapdom 配置选项
```typescript
{
  scale: 2,                    // 输出分辨率倍数
  backgroundColor: '#ffffff',  // 背景色（根据主题动态设置）
  embedFonts: false,          // 不嵌入字体（提高性能）
  fast: true,                 // 快速模式
  compress: true              // 压缩输出
}
```

#### 错误处理
- 捕获并显示下载错误
- 自动恢复 UI 状态
- 提供用户友好的错误信息

### 7. 未来改进

可能的改进方向：
- 支持更多图片格式（JPG、WebP）
- 添加图片质量选项
- 支持自定义文件名
- 添加下载进度指示器
- 支持批量下载

## 总结

成功为 LinkedIn 分享卡片添加了完整的下载功能，用户现在可以轻松地将分析结果保存为高质量的 PNG 图片进行分享或保存。该功能与现有设计完美集成，提供了良好的用户体验。
