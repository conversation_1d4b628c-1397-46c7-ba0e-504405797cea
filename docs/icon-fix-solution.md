# 图标显示问题解决方案

## 问题分析

### 原始问题
下载的图片中缺少图标，特别是：
- 下载按钮的图标 (`i-carbon:download`)
- 加载状态的图标 (`i-svg-spinners:3-dots-fade`)

### 根本原因
UnoCSS 的 `presetIcons()` 生成的图标是通过 CSS 伪元素（`::before` 或 `::after`）实现的，而不是真正的 DOM 元素。snapdom 在捕获 DOM 时可能无法正确处理这些 CSS 生成的内容。

## 解决方案

### 1. 替换 UnoCSS 图标为真实 SVG

**之前的代码**：
```vue
<div class="i-carbon:download w-4 h-4 pointer-events-none"></div>
<div class="i-svg-spinners:3-dots-fade w-4 h-4 pointer-events-none"></div>
```

**修改后的代码**：
```vue
<!-- Download icon SVG -->
<svg
  v-else
  class="w-4 h-4 pointer-events-none"
  viewBox="0 0 24 24"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
>
  <path
    d="M12 3V16M12 16L16 12M12 16L8 12M3 21H21"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
  />
</svg>

<!-- Loading spinner SVG -->
<svg
  v-if="isDownloading"
  class="w-4 h-4 pointer-events-none animate-spin"
  viewBox="0 0 24 24"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
>
  <circle
    cx="12"
    cy="12"
    r="10"
    stroke="currentColor"
    stroke-width="4"
    stroke-linecap="round"
    stroke-dasharray="31.416"
    stroke-dashoffset="31.416"
  >
    <animate
      attributeName="stroke-dasharray"
      dur="2s"
      values="0 31.416;15.708 15.708;0 31.416;0 31.416"
      repeatCount="indefinite"
    />
    <animate
      attributeName="stroke-dashoffset"
      dur="2s"
      values="0;-15.708;-31.416;-31.416"
      repeatCount="indefinite"
    />
  </circle>
</svg>
```

### 2. 优化下载配置

```typescript
const result = await snapdom(cardElement as HTMLElement, {
  scale: 2,
  backgroundColor: props.isDark ? '#141415' : '#ffffff',
  embedFonts: true,    // 内联字体，确保字体显示正确
  fast: false,         // 不使用快速模式，确保质量
  compress: false,     // 暂时不压缩，确保质量
  useProxy: '',        // 处理CORS图片
  filter: (_node: Element) => {
    return true        // 不排除任何节点
  }
})
```

### 3. 处理图片和背景图片路径

```typescript
// 预处理图片路径，确保使用绝对URL
const images = cardElement.querySelectorAll('img')
const originalSrcs: string[] = []
images.forEach((img, index) => {
  originalSrcs[index] = img.src
  if (img.src.startsWith('/')) {
    img.src = window.location.origin + img.src
  }
})

// 处理背景图片 - 找到有背景图片的元素并转换为绝对URL
const bgElements = cardElement.querySelectorAll('[class*="bg-[url"]')
const originalStyles: string[] = []
bgElements.forEach((element, index) => {
  const el = element as HTMLElement
  originalStyles[index] = el.style.backgroundImage || ''
  const classList = Array.from(el.classList)
  const bgClass = classList.find(cls => cls.includes('bg-[url'))
  if (bgClass) {
    const urlMatch = bgClass.match(/bg-\[url\(([^)]+)\)\]/)
    if (urlMatch && urlMatch[1]) {
      let url = urlMatch[1]
      if (url.startsWith('/')) {
        url = window.location.origin + url
      }
      el.style.backgroundImage = `url(${url})`
      el.style.backgroundRepeat = 'no-repeat'
      el.style.backgroundPosition = 'right center'
      el.style.backgroundSize = '700px 100%'
    }
  }
})
```

## 优势

### 1. 兼容性更好
- 真实的 SVG 元素可以被 snapdom 正确捕获
- 不依赖 CSS 伪元素生成的内容

### 2. 可控性更强
- 可以直接控制 SVG 的样式和动画
- 不受 UnoCSS 配置变化影响

### 3. 性能更好
- 减少了复杂的 DOM 操作和恢复逻辑
- 简化了下载流程

## 测试结果

✅ 图标正确显示在下载的图片中
✅ 加载动画正常工作
✅ 图片和背景图片正确显示
✅ 字体正确嵌入

## 总结

通过将 UnoCSS 图标类替换为真实的 SVG 元素，我们成功解决了下载图片中图标缺失的问题。这种方法更加可靠和可控，确保了 snapdom 能够正确捕获所有视觉元素。
