// LinkedIn API 数据类型定义
// 基于 example.json 创建的 TypeScript 接口

export interface LinkedInAnalysisResponse {
  type: string
  message: string
  data: LinkedInData
}

export interface LinkedInData {
  linkedin_id: string
  person_name: string
  linkedin_url: string
  profile_data: LinkedInProfileData
  last_updated: string
  created_at: string
  _from_cache: boolean
}

export interface LinkedInProfileData {
  role_model: LinkedInRoleModel
  money_analysis: MoneyAnalysis
  roast: string // 注意：这是一个字符串化的JSON，需要JSON.parse()解析
  skills: LinkedInSkills
  colleagues_view: ColleaguesView
  career: LinkedInCareer
  life_well_being: LifeWellBeing
  work_experience: WorkExperience[]
  education: Education[]
  about?: string // 个人简介
  personal_tags?: string[] // 个人标签
}

export interface LinkedInRoleModel {
  name: string
  institution: string
  position: string
  photo_url: string
  achievement: string
  similarity_reason: string
}

export interface MoneyAnalysis {
  estimated_salary: string
  explanation: string
}

export interface LinkedInSkills {
  industry_knowledge: string[]
  tools_technologies: string[]
  interpersonal_skills: string[]
  language: string[]
}

export interface ColleaguesView {
  highlights: string[]
  areas_for_improvement: string[]
}

export interface LinkedInCareer {
  future_development_potential: string
  development_advice: DevelopmentAdvice
}

export interface DevelopmentAdvice {
  past_evaluation: string
  future_advice: string
}

export interface LifeWellBeing {
  life_suggestion: LifeSuggestion
  health: HealthSuggestion
}

export interface LifeSuggestion {
  advice: string
  actions: WellBeingAction[]
}

export interface HealthSuggestion {
  advice: string
  actions: WellBeingAction[]
}

export interface WellBeingAction {
  emoji: string
  phrase: string
}

// 解析后的 roast 数据结构
export interface ParsedRoast {
  roast: string
}

export interface WorkExperience {
  companyId?: string
  companyUrn?: string
  companyLink1?: string
  logo?: string
  title: string
  subtitle: string
  caption: string
  metadata?: string
  breakdown: boolean
  subComponents: WorkExperienceComponent[]
}

export interface WorkExperienceComponent {
  title?: string
  caption?: string
  metadata?: string
  description: WorkDescription[]
}

export interface WorkDescription {
  type: 'textComponent' | 'mediaComponent'
  text: string
  thumbnail?: string
}

export interface Education {
  companyId?: string
  companyUrn?: string
  companyLink1?: string
  logo?: string
  title: string
  subtitle: string
  caption: string
  breakdown: boolean
  subComponents: EducationComponent[]
}

export interface EducationComponent {
  description: WorkDescription[]
}

// 用于适配现有组件的接口
export interface LinkedInUser {
  name: string
  avatar: string
  role: string
  login?: string
  bio?: string
  papers?: number
  citations?: number
  education?: SimpleEducation[]
  work_experience?: SimpleWorkExperience[]
}

export interface SimpleEducation {
  school: string
  major: string
  time: string
  logo?: string // 学校 logo URL
  companyLink1?: string // 学校 LinkedIn 链接
}

export interface SimpleWorkExperience {
  from: string
  to: string
  company: string
  position: string
  logo?: string // 公司 logo URL
  companyLink1?: string // 公司 LinkedIn 链接
}

export interface LinkedInStats {
  repositories: number
  stars: number
  pullRequests: number
}

export interface LinkedInRoleModelForCard {
  name: string
  avatar: string
  title: string
  achievement: string
}

// LinkedIn 特有的错误类型
export interface LinkedInError {
  type: 'linkedin_error'
  message: string
  code?: string
  details?: any
}

// SSE 消息类型
export interface LinkedInSSEMessage {
  type: 'start' | 'progress' | 'success' | 'end' | 'error'
  message?: string
  step?: string
  data?: LinkedInData
  error?: LinkedInError
}
