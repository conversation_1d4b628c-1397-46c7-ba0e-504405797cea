<template>
  <div class="p-8">
    <h2 class="text-2xl font-bold mb-4">Download Test</h2>
    
    <!-- Test Card -->
    <div 
      id="test-card"
      class="w-[600px] h-[400px] bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg p-6 text-white shadow-lg"
    >
      <h3 class="text-xl font-bold mb-4">Test LinkedIn Card</h3>
      <div class="flex items-center gap-4 mb-4">
        <img src="/image/avator.png" alt="Avatar" class="w-16 h-16 rounded-full" />
        <div>
          <h4 class="font-semibold"><PERSON></h4>
          <p class="text-blue-100">AI Researcher</p>
        </div>
      </div>
      <p class="text-blue-100">This is a test card for download functionality.</p>
      
      <!-- Action buttons that should be hidden during download -->
      <div class="mt-6 flex gap-3" data-action-buttons>
        <button
          @click="handleDownload"
          :disabled="isDownloading"
          class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 disabled:opacity-50"
        >
          {{ isDownloading ? 'Downloading...' : 'Download' }}
        </button>
        <button class="bg-blue-400 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-300">
          Share
        </button>
      </div>
    </div>

    <!-- Status -->
    <div class="mt-4 text-sm text-gray-600">
      <p v-if="isDownloading">正在生成图片...</p>
      <p v-if="downloadSuccess" class="text-green-600">下载成功！</p>
      <p v-if="downloadError" class="text-red-600">下载失败：{{ downloadError }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { snapdom } from '@zumer/snapdom'

const isDownloading = ref(false)
const downloadSuccess = ref(false)
const downloadError = ref('')

const handleDownload = async () => {
  if (isDownloading.value || typeof window === 'undefined') return

  isDownloading.value = true
  downloadSuccess.value = false
  downloadError.value = ''

  try {
    const cardElement = document.getElementById('test-card')
    if (!cardElement) {
      throw new Error('Card element not found')
    }

    // 隐藏操作按钮
    const actionButtons = cardElement.querySelector('[data-action-buttons]') as HTMLElement
    if (actionButtons) {
      actionButtons.style.display = 'none'
    }

    try {
      console.log('Starting snapdom capture...')
      
      // 使用 snapdom 生成图片
      const result = await snapdom(cardElement, {
        scale: 2,
        backgroundColor: '#ffffff',
        embedFonts: false,
        fast: true,
        compress: true
      })

      console.log('Snapdom capture completed, converting to blob...')

      // 转换为 PNG 并下载
      const blob = await result.toBlob({ type: 'png' })
      
      console.log('Blob created, starting download...')
      
      const link = document.createElement('a')
      link.download = `test-linkedin-card-${Date.now()}.png`
      link.href = URL.createObjectURL(blob)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)

      downloadSuccess.value = true
      console.log('Download completed successfully')

    } finally {
      // 恢复操作按钮显示
      if (actionButtons) {
        actionButtons.style.display = 'flex'
      }
    }

  } catch (error) {
    console.error('Download failed:', error)
    downloadError.value = error instanceof Error ? error.message : 'Unknown error'
  } finally {
    isDownloading.value = false
  }
}
</script>
