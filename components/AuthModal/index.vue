<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 px-4"
      @click.self="close"
    >
      <transition name="scale">
        <div class="bg-white dark:bg-[#141415] rounded-5 shadow-xl p-5 md:p-5 p-4 w-106 max-w-full text-center rel" v-show="visible">
          <h2 class="text-7 md:text-7 text-5 font-bold mb-8 md:mb-8 mb-6 mt-5 md:mt-5 mt-3 text-black dark:text-white">
            {{ isRegisterMode ? 'Sign Up' : 'Login' }}
          </h2>

          <!-- 邮箱密码表单 -->
          <div v-if="showEmailForm" class="space-y-4 px-5 md:px-5 px-2 mb-4">
            <!-- 昵称输入框 - 只在注册模式下显示 -->
            <div v-if="isRegisterMode" class="text-left">
              <input
                v-model="displayName"
                type="text"
                placeholder="Your name"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-[#1a1a1a] text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white"
                :disabled="loading"
              />
            </div>
            <div class="text-left">
              <input
                v-model="email"
                type="email"
                placeholder="Email address"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-[#1a1a1a] text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white"
                :disabled="loading"
              />
            </div>
            <div class="text-left">
              <input
                v-model="password"
                type="password"
                placeholder="Password"
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-[#1a1a1a] text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white"
                :disabled="loading"
              />
            </div>

            <!-- 错误信息 -->
            <div v-if="errorMessage" class="text-red-500 text-sm text-left">
              {{ errorMessage }}
            </div>

            <!-- 登录/注册按钮 -->
            <button
              @click="handleEmailAuth"
              class="auth-btn bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black"
              :disabled="loading || !email || !password || (isRegisterMode && !displayName)"
            >
              <div
                v-if="loading && currentProvider === 'email'"
                class="i-svg-spinners:180-ring wh-6 md:wh-6 w-5 h-5"
              ></div>
              <span v-else>{{ isRegisterMode ? 'Sign Up' : 'Login' }}</span>
            </button>

            <!-- 忘记密码 -->
            <div v-if="!isRegisterMode" class="text-center bg-transparent">
              <button
                @click="handleForgotPassword"
                class="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 text-sm hover:underline bg-transparent"
                :disabled="loading || resetPasswordCooldown > 0"
              >
                {{
                  resetPasswordCooldown > 0 ? `Wait ${resetPasswordCooldown}s` : 'Forgot password?'
                }}
              </button>
            </div>

            <!-- 切换登录/注册模式 -->
            <div class="text-center text-sm text-gray-500 dark:text-gray-400 bg-transparent">
              {{ isRegisterMode ? 'Already have an account?' : "Don't have an account?" }}
              <button
                @click="toggleMode"
                class="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 hover:underline ml-1 bg-transparent"
                :disabled="loading"
              >
                {{ isRegisterMode ? 'Login' : 'Sign Up' }}
              </button>
            </div>

            <!-- 分隔线 -->
            <div class="flex items-center my-4 bg-transparent">
              <div class="flex-1 border-t border-gray-300 dark:border-gray-600"></div>
              <span class="px-3 text-gray-500 dark:text-gray-400 text-sm bg-white dark:bg-[#141415]">or</span>
              <div class="flex-1 border-t border-gray-300 dark:border-gray-600"></div>
            </div>
          </div>

          <!-- Google登录按钮 -->
          <div class="space-y-5 md:space-y-5 space-y-4 px-5 md:px-5 px-2 mb-4">
            <button
              @click="handleAuth('google')"
              class="auth-btn bg-white dark:bg-[#141415] border border-gray-1500 dark:border-gray-600 hover:bg-black/5 dark:hover:bg-gray-700"
              :disabled="loading && currentProvider === 'google'"
            >
              <div
                v-if="loading && currentProvider === 'google'"
                class="i-svg-spinners:180-ring wh-6 md:wh-6 w-5 h-5"
              ></div>
              <div v-else class="i-devicon:google wh-6 md:wh-6 w-5 h-5"></div>
              <span class="text-black dark:text-white text-lg md:text-lg text-base">
                <span class="hidden md:inline">Continue with Google</span>
                <span class="md:hidden">Google</span>
              </span>
            </button>

            <!-- 邮箱登录切换按钮 -->
            <button
              v-if="!showEmailForm"
              @click="showEmailForm = true"
              class="auth-btn bg-white dark:bg-[#141415] border border-gray-1500 dark:border-gray-600 hover:bg-black/5 dark:hover:bg-gray-700"
            >
              <div class="i-material-symbols:email wh-6 md:wh-6 w-5 h-5"></div>
              <span class="text-black dark:text-white text-lg md:text-lg text-base">
                <span class="hidden md:inline">Continue with Email</span>
                <span class="md:hidden">Email</span>
              </span>
            </button>
          </div>
          
          <!-- 添加用户协议提示 -->
          <div class="text-center text-xs md:text-sm text-gray-500 dark:text-gray-400 px-5 md:px-5 px-2 mb-4 bg-transparent">
            Login, you agree to our
            <NuxtLink to="/terms" class="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 hover:underline bg-transparent" @click="close">Terms of Service</NuxtLink>
            and
            <NuxtLink to="/privacy" class="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 hover:underline bg-transparent" @click="close">Privacy Policy</NuxtLink>.
          </div>
          
          <div class="abs top-5 right-5">
            <div
              class="wh-6 text-#333 i-material-symbols:close cursor-pointer opacity-32 dark:text-white"
              @click="close"
            ></div>
          </div>
        </div>
      </transition>
    </div>
  </transition>

  <!-- 通知模态框 -->
  <SuccessModal
    :visible="notificationModal.state.value.visible"
    :type="notificationModal.state.value.type"
    :title="notificationModal.state.value.title"
    :message="notificationModal.state.value.message"
    :button-text="notificationModal.state.value.buttonText"
    :show-close-button="notificationModal.state.value.showCloseButton"
    :allow-backdrop-close="notificationModal.state.value.allowBackdropClose"
    @update:visible="notificationModal.hide"
    @close="notificationModal.hide"
  />
</template>

<script setup lang="ts">
  import { defineProps, defineEmits, ref, defineExpose, watch, computed, onUnmounted } from 'vue'
  import { isValidEmail } from '~/utils/validation'

  const props = defineProps<{
    visible: boolean
    loading?: boolean
    provider?: 'google' | 'twitter' | 'email' | null
  }>()

  const emit = defineEmits<{
    (e: 'update:visible', val: boolean): void
    (e: 'auth', provider: 'google' | 'twitter'): void
    (e: 'emailAuth', data: { email: string, password: string, isRegister: boolean }): void
    (e: 'forgotPassword', email: string): void
  }>()

  const { loginWithEmail, registerWithEmail, resetPassword, error: authError, loading: authLoading } = useFirebaseAuth()
  const notificationModal = useNotificationModal()

  // 表单状态
  const currentProvider = ref<'google' | 'twitter' | 'email' | null>(null)

  // 使用内部的 loading 状态，优先使用 useFirebaseAuth 的 loading
  const loading = computed(() => props.loading || authLoading.value)
  const showEmailForm = ref(false)
  const isRegisterMode = ref(false)
  const displayName = ref('')
  const email = ref('')
  const password = ref('')
  const errorMessage = ref('')
  const resetPasswordCooldown = ref(0)
  let resetPasswordTimer = null

  // 监听visible变化，重置表单
  watch(() => props.visible, (newVal) => {
    if (newVal) {
      resetForm()
    }
  })

  const resetForm = () => {
    showEmailForm.value = false
    isRegisterMode.value = false
    displayName.value = ''
    email.value = ''
    password.value = ''
    errorMessage.value = ''
    currentProvider.value = null

    // 重置密码重置倒计时
    resetPasswordCooldown.value = 0
    if (resetPasswordTimer) {
      clearInterval(resetPasswordTimer)
      resetPasswordTimer = null
    }
  }

  const close = () => {
    emit('update:visible', false)
    resetForm()
  }

  const handleAuth = (provider: 'google' | 'twitter') => {
    currentProvider.value = provider
    emit('auth', provider)
  }



  const handleEmailAuth = async () => {
    // 验证必填字段
    if (!email.value || !password.value) {
      errorMessage.value = 'Please fill in all fields'
      return
    }

    // 验证邮箱格式
    if (!isValidEmail(email.value)) {
      errorMessage.value = 'Please enter a valid email address'
      return
    }

    if (isRegisterMode.value && !displayName.value) {
      errorMessage.value = 'Please enter your name'
      return
    }

    currentProvider.value = 'email'
    errorMessage.value = ''

    try {
      if (isRegisterMode.value) {
        await registerWithEmail(email.value, password.value, displayName.value)
        // 注册成功后显示验证邮件提示
        notificationModal.showSuccess(
          'Account created successfully! A verification email has been sent to your inbox. Please check your email and click the verification link to complete your account setup.',
          'Registration Successful'
        )
      } else {
        await loginWithEmail(email.value, password.value)
      }
      // 成功后关闭模态框
      close()
    } catch (error: any) {
      // 使用 useFirebaseAuth 中处理过的错误信息
      errorMessage.value = authError.value || 'An unexpected error occurred. Please try again.'
    } finally {
      currentProvider.value = null
    }
  }

  const handleForgotPassword = async () => {
    if (resetPasswordCooldown.value > 0) return

    if (!email.value) {
      errorMessage.value = 'Please enter your email address first'
      return
    }

    // 验证邮箱格式
    if (!isValidEmail(email.value)) {
      errorMessage.value = 'Please enter a valid email address'
      return
    }

    try {
      await resetPassword(email.value)
      errorMessage.value = ''
      notificationModal.showInfo(
        'Password reset email sent! Please check your inbox.',
        'Password Reset'
      )

      // 启动30秒倒计时
      startResetPasswordCooldown()
    } catch (error: any) {
      // 使用 useFirebaseAuth 中处理过的错误信息
      errorMessage.value = authError.value || 'An unexpected error occurred. Please try again.'
    }
  }

  const toggleMode = () => {
    isRegisterMode.value = !isRegisterMode.value
    displayName.value = ''
    errorMessage.value = ''
  }

  // 启动密码重置倒计时
  const startResetPasswordCooldown = () => {
    resetPasswordCooldown.value = 30

    // 清除之前的定时器
    if (resetPasswordTimer) {
      clearInterval(resetPasswordTimer)
    }

    resetPasswordTimer = setInterval(() => {
      resetPasswordCooldown.value--
      if (resetPasswordCooldown.value <= 0) {
        clearInterval(resetPasswordTimer)
        resetPasswordTimer = null
      }
    }, 1000)
  }

  const resetLoading = () => {
    currentProvider.value = null
  }

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (resetPasswordTimer) {
      clearInterval(resetPasswordTimer)
      resetPasswordTimer = null
    }
  })

  defineExpose({
    resetLoading,
  })
</script>

<style scoped>
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .scale-enter-active,
  .scale-leave-active {
    transition: transform 0.3s ease;
  }

  .scale-enter-from,
  .scale-leave-to {
    transform: scale(0.8);
  }

  .auth-btn {
    @apply rounded-10px w-full f-cer gap-2.5 md:gap-2.5 gap-2 py-3 md:py-3 py-4 font-medium text-lg md:text-lg text-base transition-colors;
  }

  .auth-btn:disabled {
    @apply opacity-70 cursor-not-allowed;
  }

  /* 移动端优化 */
  @media (max-width: 640px) {
    .auth-btn {
      @apply py-4 text-base gap-2;
    }
    
    .rounded-5 {
      border-radius: 1.25rem;
    }
  }
</style>
