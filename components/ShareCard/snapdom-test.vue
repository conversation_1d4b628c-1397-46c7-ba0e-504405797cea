<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">Snapdom 测试</h1>
    
    <!-- 测试元素 -->
    <div 
      id="test-element" 
      class="w-96 h-64 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white shadow-lg"
    >
      <h2 class="text-xl font-bold mb-2">测试卡片</h2>
      <p class="mb-4">这是一个用于测试 snapdom 截图功能的卡片。</p>
      <img 
        src="/image/newlogo1.png" 
        alt="Logo" 
        class="w-16 h-8 object-contain"
      />
      <div class="mt-4 flex items-center gap-2">
        <div class="w-4 h-4 bg-yellow-400 rounded-full"></div>
        <span class="text-sm">状态: 正常</span>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="mt-6 flex gap-4">
      <button 
        @click="testSnapdom"
        :disabled="isLoading"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {{ isLoading ? '截图中...' : '测试 Snapdom 截图' }}
      </button>
      
      <button 
        @click="downloadImage"
        :disabled="!imageBlob || isLoading"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
      >
        下载图片
      </button>
    </div>

    <!-- 结果显示 -->
    <div v-if="imageUrl" class="mt-6">
      <h3 class="text-lg font-semibold mb-2">截图结果:</h3>
      <img :src="imageUrl" alt="Screenshot" class="border rounded shadow-lg max-w-md" />
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
      错误: {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { snapdom, preCache } from '@zumer/snapdom'

const isLoading = ref(false)
const imageUrl = ref('')
const imageBlob = ref<Blob | null>(null)
const error = ref('')

const testSnapdom = async () => {
  isLoading.value = true
  error.value = ''
  imageUrl.value = ''
  imageBlob.value = null

  try {
    const element = document.getElementById('test-element')
    if (!element) {
      throw new Error('测试元素未找到')
    }

    // 预缓存资源
    await preCache(element, {
      embedFonts: true
    })

    // 等待一下确保渲染完成
    await new Promise(resolve => setTimeout(resolve, 100))

    // 使用 snapdom 截图
    const result = await snapdom(element, {
      scale: 2,
      backgroundColor: '#ffffff',
      embedFonts: true,
      compress: true,
      fast: false
    })

    // 转换为 blob
    const blob = await result.toBlob({ type: 'png' })
    imageBlob.value = blob
    
    // 创建预览 URL
    imageUrl.value = URL.createObjectURL(blob)

    console.log('Snapdom 截图成功!')

  } catch (err) {
    console.error('Snapdom 截图失败:', err)
    error.value = err instanceof Error ? err.message : '未知错误'
  } finally {
    isLoading.value = false
  }
}

const downloadImage = () => {
  if (!imageBlob.value) return

  const link = document.createElement('a')
  link.download = `snapdom-test-${Date.now()}.png`
  link.href = URL.createObjectURL(imageBlob.value)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(link.href)
}

// 清理资源
onUnmounted(() => {
  if (imageUrl.value) {
    URL.revokeObjectURL(imageUrl.value)
  }
})
</script>
