<template>
  <motion.div
    class="mt-16 f-cer w-full"
    :initial="{ opacity: 0, y: 10 }"
    :animate="{ opacity: 1, y: 0 }"
  >
    <div
      class="max-w-172 w-full mx-auto dark:bg-[#141415] bg-[#FBF7F5] rounded-2xl px-4 sm:px-8 md:px-11 py-6 rel backdrop-blur"
      :data-card-id="'github-profile-card'"
    >
      <div class="abs left-1/2 -translate-x-1/2 -top-11">
        <Avatar :src="profile.avatar" :size="90" />
      </div>
      <div class="mt-8 flex flex-col items-center gap-3">
        <div class="font-bold text-xl sm:text-2xl text-center text-black dark:text-[#FAF9F5]">
          {{ profile.name }}
        </div>
        <div class="text-sm text-[#7C7C7C] text-center px-2">
          @{{ profile.login }}
        </div>
        <div class="text-sm text-[#433D3A] dark:text-[#C6C6C6] text-center px-2 line-clamp-3">
          {{ profile.bio || 'No bio available' }}
        </div>
        <div class="text-sm text-[#433D3A] dark:text-[#C6C6C6] text-center px-2 mt-2 mb-1" v-if="profile.description && profile.description.trim()">
          {{ profile.description }}
        </div>
        <div class="flex items-center gap-2 sm:gap-2.5 w-full flex-wrap justify-center px-2">
          <Tag :title="tag" v-for="(tag, index) in profile.tags" :key="index" />
        </div>
        <div class="mt-5 cursor-pointer" @click="$emit('share-click')">
          <div class="h-[42px] w-[121px] text-4 font-600 text-white bg-[#CB7C5D] dark:bg-[#654D43] hover:bg-[#CB7C5D]/80 dark:hover:bg-[#654D43]/80 transition-colors fx-cer justify-center rounded-full gap-2">
            <div class="i-proicons:x-twitter wh-5 font-600"></div>
            Share
          </div>
        </div>
      </div>
    </div>
  </motion.div>
</template>

<script setup lang="ts">
  import { motion } from 'motion-v'

  interface GitHubProfile {
    name: string;
    login: string;
    avatar: string;
    bio: string;
    description?: string;
    tags: string[];
  }

  const props = defineProps<{
    profile: GitHubProfile
  }>()

  const emit = defineEmits<{
    'share-click': []
  }>()
</script>

<style scoped>
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  @media (max-width: 640px) {
    .max-w-172 {
      max-width: calc(100vw - 2rem);
    }
  }

  @media (min-width: 641px) {
    .max-w-172 {
      max-width: 43rem; /* 172/4 = 43rem */
    }
  }
</style> 