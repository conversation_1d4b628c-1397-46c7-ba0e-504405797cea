<template>
  <div class="typed-text-wrapper">
    <span ref="typedText"></span>
    <span v-if="showCustomCursor" class="blinking-cursor"></span>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
  import Typed from 'typed.js'

  const props = defineProps<{
    strings: string[]
    typeSpeed?: number
    backSpeed?: number
    loop?: boolean
    showCustomCursor?: boolean
  }>()
  
  const showCustomCursor = computed(() => props.showCustomCursor ?? false)

  const typedText = ref<HTMLElement | null>(null)
  let typedInstance: Typed | null = null

  const initTyped = () => {
    if (typedText.value) {
      typedInstance = new Typed(typedText.value, {
        strings: props.strings,
        typeSpeed: props.typeSpeed || 50,
        backSpeed: props.backSpeed || 50,
        loop: props.loop || false,
        showCursor: !props.showCustomCursor,
        onStringTyped: () => {
          if (typedText.value && typedText.value.parentElement) {
            const container = typedText.value.parentElement.parentElement
            if (container && container.classList.contains('typed-text-container')) {
              container.style.height = container.style.height || '73px'
            }
          }
        },
        onComplete: () => {
          if (typedText.value && typedText.value.parentElement) {
            const container = typedText.value.parentElement.parentElement
            if (container && container.classList.contains('typed-text-container')) {
              container.style.height = container.style.height || '73px'
            }
          }
        }
      })
    }
  }

  const destroyTyped = () => {
    if (typedInstance) {
      typedInstance.destroy()
      typedInstance = null
    }
  }

  onMounted(() => {
    initTyped()
  })

  watch(
    () => props.strings,
    () => {
      destroyTyped()
      initTyped()
    }
  )

  onBeforeUnmount(() => {
    destroyTyped()
  })
</script>

<style scoped>
.typed-text-wrapper {
  display: inline-block;
  width: 100%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.blinking-cursor {
  animation: blink 1s infinite;
  display: inline-block;
  width: 4px;
  height: 36px;
  background-color: currentColor;
  margin-left: 3px;
  vertical-align: baseline;
  border-radius: 1px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}
</style>
