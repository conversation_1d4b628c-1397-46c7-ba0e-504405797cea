<template>
  <div v-if="show" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="relative">
      <div
        class="rounded-2xl shadow-xl p-6 w-[1200px] h-[800px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] dark:from-[#141415] dark:to-[#141415] border border-transparent dark:border-[#27282D]"
        data-card-id="share-card-linkedin"
      >
        <div
          class="h-[752px] bg-[url(/image/linkedin-grid.png)] bg-right bg-center bg-no-repeat bg-[length:700px_100%]"
        >
          <div class="w-[850px]">
            <!-- User Info -->
            <div class="flex items-center justify-start h-[80px]" v-if="user">
              <img :src="user.avatar" class="w-20 h-20 rounded-full mr-4" @error="($event.target as HTMLImageElement).src = '/image/avator.png'" />
              <div class="flex flex-col flex-1 justify-around gap-3">
                <div class="flex items-center gap-4">
                  <h2 class="text-xl font-bold whitespace-nowrap truncate max-w-[300px]">
                    {{ user.name }}
                  </h2>
                </div>
                <div class="flex items-center gap-1">
                  <SvgIcon name="verified" class="flex-shrink-0" />
                  <span class="text-gray-600 dark:text-gray-400 text-sm">
                    {{ 
                      user?.work_experience?.[0]?.company && user?.work_experience?.[0]?.position
                        ? `${user.work_experience[0].company} - ${user.work_experience[0].position}`
                        : user?.work_experience?.[0]?.company
                        ? user.work_experience[0].company
                        : user?.work_experience?.[0]?.position
                        ? user.work_experience[0].position
                        : 'Professional'
                    }}
                  </span>
                </div>
              </div>
            </div>

                        <!-- main content -->
            <div class="flex items-stretch justify-between mt-6 gap-4 h-[588px]">
              <!-- left -->
              <div class="flex flex-col gap-2 flex-1">
                <!-- career -->
                <div
                  class="flex flex-col justify-between p-4 h-[325px] w-[450px] border border-gray-200 dark:border-[#27282D] bg-[#FFFFFF]/40 dark:bg-[#14141580] shadow-lg"
                  style="backdrop-filter: blur(14px); border-radius: 15px"
                >
                  <div>
                    <div class="fx-cer font-700 gap-2 mb-4"><SvgIcon name="goal" />Career</div>
                    <div
                      class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-2 py-3 rounded-1 dark:bg-[#292929]"
                    >
                      <div
                        class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-2 fx-cer gap-2 text-3.5 dark:text-white"
                      >
                        <SvgIcon name="promotion" /> Future Development Potential
                      </div>
                      <div
                        class="text-3.5 font-400 leading-6 text-[#4D4846] pt-2 dark:text-[#C6C6C6] line-clamp-2"
                      >
                        {{ linkedinData?.profile_data?.career?.future_development_potential || 'Professional development potential analysis not available.' }}
                      </div>
                    </div>
                    <div class="mt-4">
                      <div
                        class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-3 fx-cer gap-2 text-3.5 dark:text-[#C6C6C6]"
                      >
                        <SvgIcon name="chat-bubble" /> Development Advice & Evaluation
                      </div>
                      <div class="relative h-4">
                        <span
                          class="absolute w-25 border border-[#C88D75] top-[-2px] left-0"
                        ></span>
                        <span class="block w-full border-t-1 border-[#ECECEC] dark:border-gray-100/20"></span>
                      </div>

                      <div
                        class="text-3.5 leading-6 text-[#464646] font-400 dark:text-[#7E7E7E] leading-3.5 line-clamp-3"
                      >
                        <span class="dark:text-[#C6C6C6] font-600">Past Evaluation: </span>{{ linkedinData?.profile_data?.career?.development_advice?.past_evaluation || 'Career evaluation not available.' }}
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Life & Well-being -->
                <div
                  class="flex flex-col justify-between p-4 h-[255px] w-[450px] border border-gray-200 dark:border-[#27282D] bg-[#FFFFFF]/40 dark:bg-[#14141580] shadow-lg"
                  style="backdrop-filter: blur(14px); border-radius: 15px"
                >
                  <div>
                    <div class="fx-cer font-700 gap-2 mb-2">
                      <SvgIcon name="healthcare" />Life & Well-being
                    </div>
                    <div
                      class="text-3.5 font-400 leading-5 text-[#2C2C2C] dark:text-[#D7D7D7] line-clamp-2"
                    >
                      <span class="text-3.5 font-700">Life suggestion：</span>{{ linkedinData?.profile_data?.life_well_being?.life_suggestion?.advice || 'Life suggestion not available.' }}
                    </div>
                    <div class="w-full fx-cer justify-between border-b-1 border-[#ECECEC] dark:border-gray-100/20 pt-3 pb-3">
  <div 
    v-for="action in (linkedinData?.profile_data?.life_well_being?.life_suggestion?.actions || []).slice(0, 3)"
    :key="action.phrase"
    class="h-8 fx-cer gap-2 justify-between rounded"
  >
    <span>{{ action.emoji }}</span>
    <span class="text-3 text-[#3C3C3C] dark:text-gray-300">{{ action.phrase }}</span>
  </div>
  <!-- Fallback if no actions available -->
  <div v-if="!linkedinData?.profile_data?.life_well_being?.life_suggestion?.actions?.length" class="h-8 fx-cer gap-2 justify-between rounded">
    <span class="text-3 text-[#3C3C3C] dark:text-gray-300">No life suggestions available</span>
  </div>
</div>
                    <div
                      class="text-3.5 font-400 leading-5 text-[#2C2C2C] pt-5 dark:text-[#D7D7D7] line-clamp-2"
                    >
                      <span class="text-3.5 font-700">Health：</span>{{ linkedinData?.profile_data?.life_well_being?.health?.advice || 'Health advice not available.' }}
                    </div>
                    <div class="w-full fx-cer justify-between my-3">
  <div 
    v-for="action in (linkedinData?.profile_data?.life_well_being?.health?.actions || []).slice(0, 3)"
    :key="action.phrase"
    class="h-8 fx-cer gap-2 justify-between rounded"
  >
    <span>{{ action.emoji }}</span>
    <span class="text-3 text-[#3C3C3C] dark:text-gray-300">{{ action.phrase }}</span>
  </div>
  <!-- Fallback if no actions available -->
  <div v-if="!linkedinData?.profile_data?.life_well_being?.health?.actions?.length" class="h-8 fx-cer gap-2 justify-between rounded">
    <span class="text-3 text-[#3C3C3C] dark:text-gray-300">No health suggestions available</span>
  </div>
</div>
                  </div>
                </div>
              </div>
              <!-- right -->
              <div class="flex flex-col gap-3 flex-1">
                <!-- salary -->
                <div
                  class="flex flex-col justify-between h-[155px] w-[385px] p-4 border bg-[#FFFFFF]/40 border-gray-200 dark:border-[#27282D] dark:bg-[#14141580] shadow-lg"
                  style="backdrop-filter: blur(14px); border-radius: 15px"
                >
                  <div>
                    <div class="fx-cer font-700 gap-2 mb-4">
                      <SvgIcon name="wallet" />Estimated Salary
                    </div>
                    <div class="fx-cer flex-col justify-center">
                      <div class="text-11 text-black font-600 dark:text-white">
                        {{ linkedinData?.profile_data?.money_analysis?.estimated_salary
                           ? formatSalaryDisplay(linkedinData.profile_data.money_analysis.estimated_salary)
                           : 'Not Available' }}
                      </div>
                      <div class="text-[#7c7c7c] text-3.5 font-400 dark:text-[#7A7A7A]">Estimated Annual Earnings</div>
                    </div>
                  </div>
                </div>
                <!-- role model -->
                <div
                  class="flex flex-col w-[385px] flex-1 p-4 border bg-[#FFFFFF]/40 border-gray-200 dark:border-[#27282D] dark:bg-[#14141580] shadow-lg"
                  style="backdrop-filter: blur(14px); border-radius: 15px"
                >
                  <div>
                    <div class="fx-cer font-700 gap-2 mb-4">
                      <SvgIcon name="settings" />Role Model
                    </div>
                  </div>
                  <div>
                    <div class="flex flex-col space-y-4">
                      <div class="fx-cer gap-3 w-full">
                        <img :src="linkedinData?.profile_data?.role_model?.photo_url || '/image/avator.png'" class="w-12 h-12 rounded-full" @error="($event.target as HTMLImageElement).src = '/image/avator.png'" />
                        <div class="flex flex-col gap-1 flex-1">
                          <div class="text-3.5 font-bold fx-cer justify-between">
                            <span>{{ linkedinData?.profile_data?.role_model?.name || 'Unknown' }}</span>
                          </div>
                          <div class="fx-cer gap-1.5 text-sm font-400 text-[#7C7C7C]">
                            <SvgIcon name="verified" />
                            <span>{{ linkedinData?.profile_data?.role_model?.position || 'Professional' }}</span>
                          </div>
                        </div>
                      </div>

                      <div
                        class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-5 rounded-1 dark:bg-[#292929]"
                      >
                        <div
                          class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-4 pb-3 fx-cer gap-2 dark:text-white"
                        >
                          Achievement:
                        </div>
                        <div
                          class="text-3.5 font-400 leading-6 text-[#4D4846] py-3 dark:text-[#C6C6C6]"
                        >
                          <!-- {{ githubData.role_model?.achievement || 'GitHub Developer' }} -->
                           {{ linkedinData?.profile_data?.role_model?.achievement || 'Achievement information not available.' }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- 底部留白区域，会自动填充剩余空间 -->
                  <div class="flex-1 min-h-[20px]"></div>
                </div>
              </div>
            </div>

            <img
              :src="user?.avatar || '@/assets/image/avator.png'"
              alt=""
              class="absolute top-[380px] right-[150px] w-[65px] h-[65px] rounded-full"
            />
          </div>
          <div class="fx-cer justify-between border-t dark:border-[#323232] h-[70px] mt-4">
            <img
              :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
              width="95"
              height="42"
              :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
            />
            <div class="flex items-center gap-3" data-action-buttons>
              <!-- Download Button -->
              <button
                :class="downloadButtonClass"
                :disabled="isDownloading"
                @click="handleDownload"
                style="backdrop-filter: blur(34px);"
              >
                <div
                  v-if="isDownloading"
                  class="i-svg-spinners:3-dots-fade w-4 h-4 pointer-events-none"
                ></div>
                <div v-else class="i-carbon:download w-4 h-4 pointer-events-none"></div>
                <span class="pointer-events-none">{{ downloadButtonText }}</span>
              </button>

              <!-- Share Button -->
              <ShareButton card-id="share-card-linkedin" :is-dark="isDark" variant="transparent" />
            </div>
          </div>
        </div>
      </div>

      <button
        class="absolute top-4 right-4 p-2 rounded-full transition-colors bg-transparent dark:bg-[#141415] hover:bg-black/10 dark:hover:bg-white/10"
        @click="$emit('close')"
      >
        <div class="i-carbon:close text-xl text-gray-500"></div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed, ref } from 'vue'
import { snapdom, preCache } from '@zumer/snapdom'
import SvgIcon from '../SvgIcon/index.vue'
import ShareButton from '../ShareButton/index.vue'

  interface User {
    name: string
    avatar: string
    role: string
    login?: string
    bio?: string
    work_experience?: Array<{
      from: string
      to: string
      company: string
      position: string
      logo?: string
      companyLink1?: string
    }>
  }

  interface Stats {
    repositories: number
    stars: number
    pullRequests: number
  }

  interface RoleModel {
    name: string
    avatar: string
    title: string
    achievement: string
  }

  interface InsightItem {
    label: string
    value: string | number
  }

  interface FeatureProject {
    name: string
    description: string
    url: string
    stargazerCount: number
    forkCount: number
    contributors: number
    used_by: number
    monthly_trending: number
  }

  interface ValuationLevel {
    salary_range: string | number[]
    level: string
    industry_ranking?: string | number
    growth_potential?: string
    reasoning?: string
    total_compensation?: string
  }

  interface MostValuablePR {
    title: string
    url: string
    repository: string
    impact: string
  }

  interface LinkedInData {
    profile_data: {
      career: {
        future_development_potential: string
        development_advice: {
          past_evaluation: string
          future_advice: string
        }
      }
      life_well_being: {
        life_suggestion: {
          advice: string
          actions: Array<{
            emoji: string
            phrase: string
          }>
        }
        health: {
          advice: string
          actions: Array<{
            emoji: string
            phrase: string
          }>
        }
      }
      money_analysis: {
        estimated_salary: string
        explanation: string
      }
      role_model: {
        name: string
        institution: string
        position: string
        photo_url: string
        achievement: string
        similarity_reason: string
      }
    }
  }

  const props = defineProps<{
    show: boolean
    user?: User
    stats?: Stats
    income?: number
    roleModel?: RoleModel
    isDark?: boolean
    insightsItems?: InsightItem[]
    languages?: Record<string, number>
    languageTotal?: number
    featureProject?: FeatureProject
    additions?: number
    deletions?: number
    valuationLevel?: ValuationLevel
    workExperience?: number
    mostValuablePR?: MostValuablePR
    linkedinData?: LinkedInData

  }>()





  // 为GithubDonut组件提供语言数据
  const languageData = computed(() => props.languages || {})
  const languageTotal = computed(() => props.languageTotal || 0)

  // 格式化数字，超过1K时显示为K单位，超过1M时显示为M单位
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'K'
    }
    return num.toString()
  }

  // 格式化薪资范围，自动转换为K/M单位
  const formatSalaryRange = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const formatSalaryValue = (value: number): string => {
        if (value >= 1000000) {
          return (value / 1000000).toFixed(1) + 'M'
        }
        if (value >= 1000) {
          return value / 1000 + 'K'
        }
        return value.toString()
      }

      return `$${formatSalaryValue(salaryRange[0])} ~ $${formatSalaryValue(salaryRange[1])}`
    }

    // 如果是字符串格式，使用原来的逻辑
    const rangeMatch = salaryRange.match(
      /\$(\d+(?:,\d{3})*(?:[KkMm])?)\s*-\s*\$(\d+(?:,\d{3})*(?:[KkMm])?)/
    )

    if (rangeMatch) {
      const [, minStr, maxStr] = rangeMatch

      const formatSalaryValue = (value: string): string => {
        // 如果已经包含K或M，直接返回
        if (value.includes('K') || value.includes('k')) {
          return value.toUpperCase()
        }
        if (value.includes('M') || value.includes('m')) {
          return value.toUpperCase()
        }

        // 移除逗号并转换为数字
        const num = parseInt(value.replace(/,/g, ''))

        if (num >= 1000000) {
          return (num / 1000000).toFixed(1) + 'M'
        }
        if (num >= 1000) {
          return num / 1000 + 'K'
        }

        return num.toString()
      }

      return `$${formatSalaryValue(minStr)} ~ $${formatSalaryValue(maxStr)}`
    }

    // 如果格式不匹配，返回原始值
    return salaryRange
  }

  // 格式化薪资最低值，只返回数字部分（不包含$和K/M）
  const formatMinSalary = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const minValue = salaryRange[0]
      if (minValue >= 1000000) {
        return (minValue / 1000000).toFixed(1)
      }
      if (minValue >= 1000) {
        return (minValue / 1000).toString()
      }
      return minValue.toString()
    }

    // 如果是字符串格式，提取最低值
    const rangeMatch = salaryRange.match(/\$(\d+(?:,\d{3})*(?:[KkMm])?)/)

    if (rangeMatch) {
      const minStr = rangeMatch[1]

      // 如果已经包含M，移除M并返回数字
      if (minStr.includes('M') || minStr.includes('m')) {
        return minStr.replace(/[Mm]/g, '')
      }

      // 如果已经包含K，移除K并返回数字
      if (minStr.includes('K') || minStr.includes('k')) {
        return minStr.replace(/[Kk]/g, '')
      }

      // 移除逗号并转换为数字，然后转换为K/M单位
      const num = parseInt(minStr.replace(/,/g, ''))

      if (num >= 1000000) {
        return (num / 1000000).toFixed(1)
      }
      if (num >= 1000) {
        return (num / 1000).toString()
      }

      return num.toString()
    }

    // 如果格式不匹配，返回默认值
    return '120'
  }

  // 格式化薪资显示，将 - 替换为 ~ 并应用数字格式化
  const formatSalaryDisplay = (salaryRange: string | number): string => {
    if (!salaryRange) return ''

    // 如果是数字，直接格式化
    if (typeof salaryRange === 'number') {
      return '$' + formatNumber(salaryRange)
    }

    // 先替换分隔符
    let formatted = salaryRange.replace(/-/g, '~')

    // 匹配薪资数字并格式化
    // 匹配格式如: "50000~80000 USD/month", "$50000~80000/month", "50000 USD/month"
    formatted = formatted.replace(/(\d+(?:,\d{3})*)/g, (match) => {
      const num = parseInt(match.replace(/,/g, ''))
      return formatNumber(num)
    })

    return formatted
  }

  // 格式化薪资最低值，包含K/M后缀
  const formatMinSalaryWithSuffix = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const minValue = salaryRange[0]
      if (minValue >= 1000000) {
        return (minValue / 1000000).toFixed(1) + 'M'
      }
      if (minValue >= 1000) {
        return (minValue / 1000).toString() + 'K'
      }
      return minValue.toString()
    }

    // 如果是字符串格式，提取最低值
    const rangeMatch = salaryRange.match(/\$(\d+(?:\.\d+)?(?:,\d{3})*(?:[KkMm])?)/)

    if (rangeMatch) {
      const minStr = rangeMatch[1]

      // 如果已经包含M，直接返回
      if (minStr.includes('M') || minStr.includes('m')) {
        return minStr.replace(/[Mm]/g, 'M')
      }

      // 如果已经包含K，直接返回
      if (minStr.includes('K') || minStr.includes('k')) {
        return minStr.replace(/[Kk]/g, 'K')
      }

      // 移除逗号并转换为数字，然后转换为K/M单位
      const num = parseInt(minStr.replace(/,/g, ''))

      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toString() + 'K'
      }

      return num.toString()
    }

    // 如果格式不匹配，返回默认值
    return '120K'
  }

  // 下载相关的响应式变量
  const isDownloading = ref(false)

  // 计算下载按钮文字
  const downloadButtonText = computed(() => {
    if (isDownloading.value) {
      return 'Downloading...'
    }
    return 'Download'
  })

  // 计算下载按钮样式
  const downloadButtonClass = computed(() => {
    const baseClass = 'flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed'

    if (props.isDark) {
      return `${baseClass} bg-[#14141580] border border-[#27282D] text-[#FAF9F5] shadow-sm hover:bg-[#27282D] disabled:hover:bg-[#14141580]`
    } else {
      return `${baseClass} bg-[#FFFFFF]/60 border border-gray-200 text-gray-700 hover:bg-[#F5F5F5] disabled:hover:bg-[#FFFFFF]/60`
    }
  })

  // 下载功能
  const handleDownload = async () => {
    if (isDownloading.value || typeof window === 'undefined') return

    isDownloading.value = true
    try {
      await downloadFromScreenshot()
    } catch (error) {
      console.error('Download failed:', error)
      alert('Screenshot failed. This might be due to CORS issues with external images. Please try again or contact support.')
    } finally {
      isDownloading.value = false
    }
  }

  // 从截图生成下载
  const downloadFromScreenshot = async () => {
    const cardElement = document.querySelector('[data-card-id="share-card-linkedin"]')
    if (!cardElement) {
      throw new Error('Card element not found')
    }

    // 隐藏操作按钮
    const actionButtons = cardElement.querySelector('[data-action-buttons]') as HTMLElement
    if (actionButtons) {
      actionButtons.style.display = 'none'
    }

    try {
      // 等待一小段时间确保所有 UnoCSS 图标和字体都已渲染
      console.log('Waiting for icons and fonts to render...')
      await new Promise(resolve => setTimeout(resolve, 500))

      // 预缓存资源（图片和字体）
      console.log('Pre-caching resources...')
      await preCache(cardElement as HTMLElement, {
        embedFonts: true,  // 内联字体
        useProxy: ''       // 如果有CORS问题，可以设置代理
      })

      console.log('Generating screenshot...')
      // 使用 snapdom 生成图片
      const result = await snapdom(cardElement as HTMLElement, {
        scale: 2,
        backgroundColor: props.isDark ? '#141415' : '#ffffff',
        embedFonts: true,    // 内联字体，确保字体显示正确
        fast: false,         // 不使用快速模式，确保质量
        compress: false,     // 暂时不压缩，确保质量
        useProxy: '',        // 处理CORS图片，如果需要可以设置代理
        // 确保包含所有伪元素和生成的内容
        filter: (_node: Element) => {
          // 不排除任何节点，确保 UnoCSS 图标被包含
          return true
        }
      })

      console.log('Converting to blob...')
      // 转换为 PNG 并下载
      const blob = await result.toBlob({ type: 'png' })
      await downloadFromBlob(blob)

    } finally {
      // 恢复操作按钮显示
      if (actionButtons) {
        actionButtons.style.display = 'flex'
      }
    }
  }

  // 从Blob下载图片
  const downloadFromBlob = async (blob: Blob) => {
    const link = document.createElement('a')
    link.download = `dinq-linkedin-analysis-${Date.now()}.png`
    link.href = URL.createObjectURL(blob)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href) // 清理内存
  }
</script>

<style scoped>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
