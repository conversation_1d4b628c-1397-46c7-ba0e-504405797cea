<template>
  <div
    class="linkedin-card rounded-2xl p-5 flex flex-col items-center text-center w-[280px] h-[234px] mx-auto overflow-hidden"
  >
    <div class="relative flex items-center justify-center">
      <!-- 加载状态骨架屏 -->
      <div
        v-show="isLoading && !imageError"
        class="avatar-skeleton rounded-full"
        style="width: 56px; height: 56px"
      ></div>

      <!-- 主要头像图片 -->
      <NuxtImg
        v-show="!imageError && !isLoading"
        :src="talent.photo_url || '/image/avator.png'"
        preload
        :alt="talent.name || 'LinkedIn User'"
        style="width: 56px; height: 56px"
        class="rounded-full object-cover border-white text-center shadow avatar-fade-in"
        @error="handleImageError"
        @load="handleImageLoad"
      />

      <!-- 默认头像 -->
      <img
        v-show="imageError"
        src="/image/avator.png"
        style="width: 56px; height: 56px"
        :alt="talent.name || 'LinkedIn User'"
        class="rounded-full object-cover avatar-fade-in"
      />
    </div>
    <div
      class="text-xl font-bold text-gray-900 truncate max-w-full"
      style="font-family: 'Poppins', 'Inter', sans-serif; font-size: 18px; margin-top: 8px; font-weight: 700"
    >
      {{ talent.name || 'LinkedIn User' }}
    </div>
    <div
      class="position-text text-base mb-4 break-words line-clamp-3 max-h-20 overflow-hidden"
      style="font-size: 12px"
    >
      {{ talent.title || 'Professional' }}, {{ talent.company || 'Company' }}
    </div>
    <div class="flex gap-4 w-full mt-auto justify-center">
      <button
        class="analyze-btn rounded-xl text-sm transition-all"
        style="width: 115px; height: 38px; font-size: 14px"
        @click.stop="handleAnalyze"
      >
        Analyze
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  
  interface LinkedInTalent {
    name: string
    title: string  // 对应LinkedIn API的title字段
    company: string
    photo_url: string  // 对应LinkedIn API的photo_url字段
    linkedin_url: string
    bio?: string
    skills?: string[]
    location?: string
    experience_years?: number
    industry?: string
  }

  const props = defineProps<{
    talent: LinkedInTalent
    index?: number
  }>()

  const emit = defineEmits<{
    search: [linkedin_url: string]
  }>()
  
  const imageError = ref(false)
  const isLoading = ref(true)

  function handleImageError() {
    imageError.value = true
    isLoading.value = false
  }

  function handleImageLoad() {
    isLoading.value = false
  }
  
  function handleAnalyze() {
    emit('search', props.talent.linkedin_url || '')
  }
</script>

<style scoped>
  .linkedin-card {
    background: #FFFFFF99;
    backdrop-filter: blur(34px);
    position: relative;
  }

  .linkedin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    padding: 1px;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    pointer-events: none;
  }

  .dark .linkedin-card {
    background: #141415;
    border: 1px solid #27282D;
    box-shadow: 0px 3px 8px 0px #0000001A;
    backdrop-filter: none;
  }

  .dark .linkedin-card::before {
    display: none;
  }

  .analyze-btn {
    background: #000000;
    color: #FFFFFF;
    font-weight: 500;
    border: none;
  }

  .analyze-btn:hover {
    background: #333333;
  }

  .dark .analyze-btn {
    background: #FAF9F5;
    color: #000000;
    border: none;
  }

  .dark .analyze-btn:hover {
    background: #E8E7E3;
  }

  .position-text {
    color: #666;
    line-height: 1.4;
  }

  .dark .position-text {
    color: #ccc;
  }

  /* 头像加载效果 */
  .avatar-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }

  .dark .avatar-skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  .avatar-fade-in {
    animation: fadeIn 0.8s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* 多行省略（line-clamp）兼容性处理 */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 卡片动画效果 */
  .linkedin-card {
    animation: cardSlideIn 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
  }

  @keyframes cardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .linkedin-card {
      width: 100%;
      max-width: 280px;
    }
  }
</style>
