<template>
  <div class="px-8 py-6 max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-6 text-center">LinkedIn Profile Fields Test</h1>
    
    <div class="mb-8">
      <button 
        @click="loadTestData" 
        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium"
      >
        Load Test Data
      </button>
    </div>

    <!-- Profile Card Test -->
    <div v-if="testData" class="space-y-8">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <h2 class="text-xl font-semibold mb-4">Profile Card Preview</h2>
        <GitHubProfileCard :profile="profileData" @share-click="() => {}" />
      </div>

      <!-- Field Details -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <h2 class="text-xl font-semibold mb-4">Field Details</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="font-medium text-gray-700 dark:text-gray-300 mb-2">About Field:</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded">
              {{ testData.profile_data?.about || 'No about field found' }}
            </p>
          </div>
          
          <div>
            <h3 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Personal Tags:</h3>
            <div class="flex flex-wrap gap-2">
              <span 
                v-for="tag in testData.profile_data?.personal_tags || []" 
                :key="tag"
                class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-full"
              >
                {{ tag }}
              </span>
              <span v-if="!testData.profile_data?.personal_tags?.length" class="text-gray-500 text-sm">
                No personal tags found
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Raw Data -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <h2 class="text-xl font-semibold mb-4">Raw Data Structure</h2>
        <pre class="text-xs bg-gray-50 dark:bg-gray-700 p-4 rounded overflow-auto max-h-96">{{ JSON.stringify(testData, null, 2) }}</pre>
      </div>
    </div>

    <div v-else class="text-center text-gray-500 py-12">
      Click "Load Test Data" to see the profile card with new fields
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import GitHubProfileCard from '~/components/GitHubProfileCard/index.vue'

const testData = ref<any>(null)

const profileData = computed(() => {
  if (!testData.value) return null
  
  return {
    name: testData.value.person_name,
    login: testData.value.linkedin_id?.replace('linkedin:', '') || '',
    avatar: testData.value.profile_data?.role_model?.photo_url || '/image/avator.png',
    bio: testData.value.profile_data?.about || '',
    description: '', // Not used anymore
    tags: testData.value.profile_data?.personal_tags || []
  }
})

const loadTestData = async () => {
  try {
    const response = await fetch('/example.json')
    const data = await response.json()
    testData.value = data.data
  } catch (error) {
    console.error('Failed to load test data:', error)
    alert('Failed to load test data. Make sure example.json exists.')
  }
}

// SEO Meta
useSeoMeta({
  title: 'LinkedIn Profile Fields Test - DINQ',
  description: 'Test page for LinkedIn profile about field and personal tags'
})
</script>

<style scoped>
/* Add any custom styles here */
</style>
