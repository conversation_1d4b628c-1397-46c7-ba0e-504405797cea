<template>
  <div class="px-30 pt-7.5 h-full">
    <template v-if="loading">
      <!-- 骨架屏 -->
      <div class="space-y-7.5 flex flex-col items-center relative">
        <Loading
          :visible="loading"
          :data="thinking"
          @update:visible="router.replace('/analysis')"
        />
        <!-- Profile Card 骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 relative animate-pulse w-1/2">
          <!-- 头像和基本信息 -->
          <div class="flex items-start gap-7.5">
            <div class="w-25 h-25 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
            <div class="flex-1 space-y-4">
              <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-1/3"></div>
              <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
              <div class="flex flex-wrap gap-2.5">
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-28"></div>
                <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
              </div>
            </div>
            <div class="flex gap-3">
              <div class="w-10 h-10 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
              <div class="w-10 h-10 rounded-full bg-gray-200/30 dark:bg-gray-600/30"></div>
            </div>
          </div>

          <!-- 统计数据 -->
          <div class="mt-7.5 grid grid-cols-4 gap-7.5">
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
            <div class="space-y-2.5">
              <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/2"></div>
            </div>
          </div>
        </div>

        <!-- Most Cited Papers 骨架屏 -->
        <div class="w-full bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 animate-pulse min-h-80">
          <div
            class="h-10 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-1/3 mx-auto mb-7.5"
          ></div>
          <div class="flex justify-center gap-7.5 h-[calc(100%-4.5rem)]">
            <div class="w-[45%] p-7.5 rounded-2xl bg-gray-200/10 dark:bg-gray-600/10 flex flex-col">
              <div class="space-y-4">
                <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/6"></div>
                </div>
              </div>
              <div class="mt-auto space-y-4">
                <div class="h-20 bg-gray-200/20 dark:bg-gray-600/20 rounded-lg w-full"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/3"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                </div>
              </div>
            </div>
            <div class="w-[45%] p-7.5 rounded-2xl bg-gray-200/10 dark:bg-gray-600/10 flex flex-col">
              <div class="space-y-4">
                <div class="h-7 bg-gray-200/30 dark:bg-gray-600/30 rounded-lg w-3/4"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/6"></div>
                </div>
              </div>
              <div class="mt-auto space-y-4">
                <div class="h-20 bg-gray-200/20 dark:bg-gray-600/20 rounded-lg w-full"></div>
                <div class="flex items-center justify-between">
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/3"></div>
                  <div class="h-5 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="linkedinData">
      <div class="space-y-7.5" v-if="linkedinData && !loading">
        <!-- LinkedIn Profile Card -->
        <GitHubProfileCard :profile="linkedinProfileData" @share-click="showPopup = true" />

        <Teleport to="body">
          <ShareCardLinkedin
            ref="shareCardRef"
            :show="showPopup"
            :user="user"
            :income="
              linkedinData?.profile_data?.money_analysis?.estimated_salary
                ? (() => {
                    const salary = linkedinData.profile_data.money_analysis.estimated_salary
                    // 处理各种薪资格式: '30K - 50K', '30000 - 50000', '$30K - $50K'
                    const match = salary.match(/\$?(\d+(?:,\d{3})*)([KkMm])?\s*[-~]\s*\$?(\d+(?:,\d{3})*)([KkMm])?/)
                    if (match) {
                      let minSalary = parseInt(match[1].replace(/,/g, ''))
                      const minUnit = match[2]?.toUpperCase()

                      if (minUnit === 'K') {
                        minSalary *= 1000
                      } else if (minUnit === 'M') {
                        minSalary *= 1000000
                      }

                      return minSalary
                    }

                    // 处理单个数值: '50K', '$50000'
                    const singleMatch = salary.match(/\$?(\d+(?:,\d{3})*)([KkMm])?/)
                    if (singleMatch) {
                      let amount = parseInt(singleMatch[1].replace(/,/g, ''))
                      const unit = singleMatch[2]?.toUpperCase()

                      if (unit === 'K') {
                        amount *= 1000
                      } else if (unit === 'M') {
                        amount *= 1000000
                      }

                      return amount
                    }

                    return 50000
                  })()
                : 50000
            "
            :is-dark="isDark"
            :stats="stats"
            :role-model="roleModel"
            :languages="{}"
            :language-total="0"
            :feature-project="undefined"
            :additions="0"
            :deletions="0"
            :valuation-level="undefined"
            :work-experience="linkedinData?.profile_data?.work_experience?.length || 0"
            :most-valuable-p-r="undefined"
            :linkedin-data="linkedinData"

            @close="showPopup = false"
            :insights-items="items"
          />
        </Teleport>



        <div class="grid grid-cols-2 gap-7.5">
          <!-- Education -->
          <ReportCard
            icon="graduation"
            :title="'Education'"
            :card-id="'overview'"
            @share-popup="showPopup = true"
          >
            <div v-if="user?.education && user?.education.length > 0" class="mb-4">
              <ul 
                :class="[
                  'space-y-2 max-h-[230px] m-0',
                  user?.education && user.education.length > 3 ? 'overflow-y-auto py-1' : 'overflow-y-visible p-0'
                ]"
              >
                <li v-for="(item, i) in user?.education" :key="i" class="text-sm fx-cer gap-2 h-18">
                  <div class="relative h-full fx-cer flex-col w-2 justify-center">
                    <span
                      v-if="i === 0"
                      class="absolute top-3 left-0 w-2 h-2 border rounded-full border-[#CB7C5D] bg-white z-1"
                    ></span>
                    <span
                      v-else
                      class="absolute top-4 w-2 h-2 bg-[#ccc] rounded-full z-1"
                      :class="{
                        'top-3': user?.education && i === user?.education?.length - 1,
                      }"
                    ></span>
                    <span
                      v-if="user?.education && i !== user?.education?.length - 1"
                      class="absolute top-3 h-18 mt-2 border-l border-[#ccc] border-dashed"
                    ></span>
                  </div>
                  <div class="w-full">
                    <p
                      class="fx-cer justify-between border-b border-[#EEEEEE] dark:border-[#252525]"
                    >
                      <b
                        class="fx-cer gap-2 border-b-2 pl-2 pr-4 h-12"
                        :class="{
                          'border-[#C88D75] dark:border-[#654D43]': i === 0,
                          'border-[#DCDCDC] dark:border-[#555]': i !== 0,
                        }"
                      >
                        <a
                          v-if="item.companyLink1"
                          :href="item.companyLink1"
                          target="_blank"
                          class="cursor-pointer"
                        >
                          <img
                            :src="item.logo || '/image/defaultCompany.png'"
                            :alt="item.school"
                            class="w-10.5 h-10.5 rounded-full hover:opacity-80 transition-opacity"
                            @error="($event.target as HTMLImageElement).src = '/image/defaultCompany.png'"
                          />
                        </a>
                        <img
                          v-else
                          :src="item.logo || '/image/defaultCompany.png'"
                          :alt="item.school"
                          class="w-10.5 h-10.5 rounded-full"
                                                     @error="($event.target as HTMLImageElement).src = '/image/defaultCompany.png'"
                        />
                        <a
                          v-if="item.companyLink1"
                          :href="item.companyLink1"
                          target="_blank"
                          class="dark:text-white hover:text-[#CB7C5D] dark:hover:text-[#CB7C5D] transition-colors cursor-pointer hover:underline hover:decoration-[#CB7C5D] truncate whitespace-nowrap max-w-[300px]"
                        >
                          {{ item.school }}
                        </a>
                        <span v-else class="dark:text-white truncate whitespace-nowrap max-w-[300px]">{{ item.school }}</span>
                      </b>
                      <span class="text-[#6F6F6F] text-xs dark:text-[#7A7A7A]">{{
                        item.time
                      }}</span>
                    </p>
                    <p
                      class="text-[#3C3C3C] dark:text-[#C6C6C6] text-xs font-400 leading-8 dark:text-[#7A7A7A] truncate whitespace-nowrap max-w-[450px]"
                    >
                      {{ item.major }}
                    </p>
                  </div>
                </li>
              </ul>
            </div>
            <div
              class="w-full h-[142px] bg-[#F6F2F1] rounded p-4 text-3.5 leading-6 text-[#4D4846] font-400 dark:bg-[#292929] dark:text-[#C6C6C6] line-clamp-5"
            >
              {{ linkedinData?.profile_data?.education_summary || 'Additional academic background includes specialized training in quantum computing, machine learning certifications from leading institutions, and advanced research experience across multiple top-tier universities.' }}
            </div>
          </ReportCard>

          <!-- Work Experience -->
          <ReportCard
            icon="briefcase"
            :title="'Work Experience'"
            :card-id="'Work-Experience'"
            @share-popup="showPopup = true"
          >
            <!-- 工作经历 -->
            <div v-if="user?.work_experience && user.work_experience.length > 0" class="mb-4">
              <ul 
                :class="[
                  'space-y-2 max-h-[230px] m-0',
                  user?.work_experience && user.work_experience.length > 3 ? 'overflow-y-auto py-1' : 'overflow-y-visible p-0'
                ]"
              >
                <li
                  v-for="(item, i) in user?.work_experience"
                  :key="i"
                  class="text-sm fx-cer gap-2 h-18"
                >
                  <div class="relative h-full fx-cer flex-col w-2 justify-center">
                    <span
                      v-if="i === 0"
                      class="absolute top-3 left-0 w-2 h-2 border rounded-full border-[#CB7C5D] bg-white z-1"
                    ></span>
                    <span
                      v-else
                      class="absolute top-4 w-2 h-2 bg-[#ccc] rounded-full z-1"
                      :class="{
                        'top-3': user?.work_experience && i === user?.work_experience?.length - 1,
                      }"
                    ></span>
                    <span
                      v-if="user?.work_experience && i !== user?.work_experience?.length - 1"
                      class="absolute top-3 h-18 mt-2 border-l border-[#ccc] border-dashed"
                    ></span>
                  </div>
                  <div class="w-full">
                    <p
                      class="fx-cer justify-between border-b border-[#EEEEEE] dark:border-[#252525]"
                    >
                      <b
                        class="fx-cer gap-2 border-b-2 pl-2 pr-4 h-12"
                        :class="{
                          'border-[#C88D75] dark:border-[#654D43]': i === 0,
                          'border-[#DCDCDC] dark:border-[#555]': i !== 0,
                        }"
                      >
                        <a
                          v-if="item.companyLink1"
                          :href="item.companyLink1"
                          target="_blank"
                          class="cursor-pointer"
                        >
                          <img
                            :src="item.logo || '/image/defaultCompany.png'"
                            :alt="item.company"
                            class="w-10.5 h-10.5 rounded-full hover:opacity-80 transition-opacity"
                            @error="($event.target as HTMLImageElement).src = '/image/defaultCompany.png'"
                          />
                        </a>
                        <img
                          v-else
                          :src="item.logo || '/image/defaultCompany.png'"
                          :alt="item.company"
                          class="w-10.5 h-10.5 rounded-full"
                                                     @error="($event.target as HTMLImageElement).src = '/image/defaultCompany.png'"
                        />
                        <a
                          v-if="item.companyLink1"
                          :href="item.companyLink1"
                          target="_blank"
                          class="dark:text-white hover:text-[#CB7C5D] dark:hover:text-[#CB7C5D] transition-colors cursor-pointer hover:underline hover:decoration-[#CB7C5D] truncate whitespace-nowrap max-w-[300px]"
                        >
                          {{ item.company }}
                        </a>
                        <span v-else class="dark:text-white truncate whitespace-nowrap max-w-[300px]">{{ item.company }}</span>
                      </b>
                      <span class="text-[#6F6F6F] text-xs dark:text-[#7A7A7A]"
                        >{{ item.from }}-{{ item.to }}</span
                      >
                    </p>
                    <p
                      class="text-[#3C3C3C] dark:text-[#C6C6C6] text-xs font-400 leading-8 dark:text-[#7A7A7A] truncate whitespace-nowrap max-w-[450px]"
                    >
                      {{ item.position }}
                    </p>
                  </div>
                </li>
              </ul>
            </div>
            <div
              class="w-full h-[142px] bg-[#F6F2F1] rounded p-4 text-3.5 leading-6 text-[#4D4846] font-400 dark:bg-[#292929] dark:text-[#C6C6C6] line-clamp-5"
            >
              {{ linkedinData?.profile_data?.work_experience_summary || 'Other Work Experiences Served as a technical expert and researcher at renowned companies such as Microsoft, OpenAI, and Google, where I accumulated extensive experience in big data processing, intelligent hardware development, and other fields.' }}
            </div>
          </ReportCard>

          <!-- Skills -->
          <ReportCard
            icon="pencil"
            :title="'Skills'"
            :x="-50"
            :card-id="'Skills'"
            @share-popup="showPopup = true"
          >
            <div
              class="h-10 mb-5 fx-cer gap-2 p-2 bg-[#FFFCF7] rounded text-3 text-[#666666] dark:bg-[#30302E] dark:text-[#C6C6C6]"
            >
              <img src="~/assets/image/info.svg" alt="" class="w-4 h-4" />
              <span>Display top skills filtered by AI based on value and recognition</span>
            </div>
            <div class="grid grid-cols-2 grid-rows-2 gap-2">
              <div
                class="flex-1 h-[195px] rounded bg-[#EBEDF2] bg-[url(/image/circle-quarter.png)] bg-no-repeat bg-[length:100px_122px] bg-bottom-right dark:bg-[#292929] dark:bg-[url(@/assets/image/Group3x.png)] dark:bg-contain dark:bg-bottom flex flex-col gap-4 p-4"
              >
                <div class="fx-cer gap-2 text-3.5 font-400">
                  <span
                    class="bg-[#D0D5E3] w-6 h-6 fx-cer justify-center rounded-1 dark:bg-[#42444A]"
                    ><SvgIcon name="idea1"
                  /></span>
                  Industry Knowledge
                </div>
                <div
                  v-for="(item, index) in (linkedinData?.profile_data?.skills?.industry_knowledge || []).slice(0, 4)"
                  :key="index"
                  class="text-[#5A5554] text-3 fx-cer gap-2 dark:text-[#C6C6C6]"
                >
                  <span class="h-1 w-1 inline-block rounded-full bg-[#5A5554]"></span>
                  <span>{{ item }}</span>
                </div>
              </div>
              <div
                class="flex-1 h-[195px] rounded bg-[#F1EFEB] bg-[url(/image/circle-quarter.png)] bg-no-repeat bg-[length:100px_122px] bg-bottom-right dark:bg-[#292929] flex flex-col gap-4 p-4 dark:bg-[url(@/assets/image/Group3x.png)] dark:bg-contain dark:bg-bottom"
              >
                <div class="fx-cer gap-2 text-3.5 font-400">
                  <span
                    class="bg-[#DFDEDA] w-6 h-6 fx-cer justify-center rounded-1 dark:bg-[#444444]"
                    ><SvgIcon name="blockchain"
                  /></span>
                  Tools & Technologies
                </div>
                <div
                  v-for="(item, index) in (linkedinData?.profile_data?.skills?.tools_technologies || []).slice(0, 4)"
                  :key="index"
                  class="text-[#5A5554] text-3 fx-cer gap-2 dark:text-[#C6C6C6]"
                >
                  <span class="h-1 w-1 inline-block rounded-full bg-[#5A5554]"></span>
                  <span>{{ item }}</span>
                </div>
              </div>
              <div
                class="flex-1 h-[195px] rounded bg-[#F6F1E7] bg-[url(/image/circle-quarter.png)] bg-no-repeat bg-[length:100px_122px] bg-bottom-right dark:bg-[#292929] flex flex-col gap-4 p-4 dark:bg-[url(@/assets/image/Group3x.png)] dark:bg-contain dark:bg-bottom"
              >
                <div class="fx-cer gap-2 text-3.5 font-400">
                  <span
                    class="bg-[#E5DBC4] w-6 h-6 fx-cer justify-center rounded-1 dark:bg-[#5B5851]"
                    ><SvgIcon name="interpersonal-skills"
                  /></span>
                  Interpersonal Skills
                </div>
                <div
                  v-for="(item, index) in (linkedinData?.profile_data?.skills?.interpersonal_skills || []).slice(0, 4)"
                  :key="index"
                  class="text-[#5A5554] text-3 fx-cer gap-2 dark:text-[#C6C6C6]"
                >
                  <span class="h-1 w-1 inline-block rounded-full bg-[#5A5554]"></span>
                  <span>{{ item }}</span>
                </div>
              </div>
              <div
                class="flex-1 h-[195px] rounded bg-[#F6E6DF] bg-[url(/image/circle-quarter.png)] bg-no-repeat bg-[length:100px_122px] bg-bottom-right dark:bg-[#292929] flex flex-col gap-4 p-4 dark:bg-[url(@/assets/image/Group3x.png)] dark:bg-contain dark:bg-bottom"
              >
                <div class="fx-cer gap-2 text-3.5 font-400">
                  <span
                    class="bg-[#F1D5CB] w-6 h-6 fx-cer justify-center rounded-1 dark:bg-[#433F3D]"
                    ><SvgIcon name="global"
                  /></span>
                  Language
                </div>
                <div
                  v-for="(item, index) in (linkedinData?.profile_data?.skills?.language || []).slice(0, 4)"
                  :key="index"
                  class="text-[#5A5554] text-3 fx-cer gap-2 dark:text-[#C6C6C6]"
                >
                  <span class="h-1 w-1 inline-block rounded-full bg-[#5A5554]"></span>
                  <span>{{ item }}</span>
                </div>
              </div>
            </div>
          </ReportCard>

          <!-- role model -->
          <ReportCard
            icon="settings"
            :title="'Role Model'"
            :x="-50"
            :card-id="'role-model'"
            @share-popup="showPopup = true"
          >
            <div class="flex flex-col h-full space-y-4">
              <div class="fx-cer gap-4 w-full">
                <Avatar
                  :src="roleModel.avatar"
                  :size="70"
                />
                <div class="flex flex-col gap-1 flex-1">
                  <div class="text-2xl font-bold">
                    <span>{{ roleModel.name }}</span>
                  </div>
                  <div class="fx-cer gap-1.5 text-sm text-gray-900">
                    <SvgIcon name="verified" />
                    <span>{{ roleModel.title }}</span>
                  </div>
                </div>
              </div>

              <div
                class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-5 rounded-1 dark:bg-[#292929] dark:text-[#C6C6C6]"
              >
                <div
                  class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-3 fx-cer gap-2 dark:text-[#C6C6C6]"
                >
                  <SvgIcon name="medal" /> Achievement:
                </div>
                <div class="text-3.5 font-400 leading-6 text-[#4D4846] py-3 dark:text-[#C6C6C6]">
                  {{ roleModel.achievement }}
                </div>
              </div>

              <div
                class="text-3.5 font-400 rounded-1 leading-6 text-[#4D4846] p-3 bg-[#FAF2EF] dark:bg-[#292929] dark:text-[#C6C6C6] line-clamp-8"
                v-if="linkedinData?.profile_data?.role_model?.similarity_reason"
              >
                {{ linkedinData.profile_data.role_model.similarity_reason }}
              </div>
              <div
                class="text-3.5 font-400 rounded-1 leading-6 text-[#4D4846] p-3 bg-[#FAF2EF] dark:bg-[#292929] dark:text-[#C6C6C6] text-center text-gray-500 line-clamp-8"
                v-else
              >
                No detailed information available
              </div>
            </div>
          </ReportCard>

          <!-- Career -->
          <ReportCard
            icon="goal"
            :title="'Career'"
            :card-id="'Career'"
            @share-popup="showPopup = true"
          >
            <div
              class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-5 rounded-1 dark:bg-[#292929]"
            >
              <div
                class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-3 fx-cer gap-2 dark:text-white"
              >
                <SvgIcon name="promotion" /> Future Development Potential
              </div>
              <div
                class="text-3.5 font-400 leading-6 text-[#4D4846] dark:text-[#C6C6C6] pt-3 dark:text-[#C6C6C6]"
              >
                {{ linkedinData?.profile_data?.career?.future_development_potential || 'Professional development potential analysis not available.' }}
              </div>
            </div>
            <div class="mt-4">
              <div
                class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-3 fx-cer gap-2 dark:text-white"
              >
                <SvgIcon name="chat-bubble" /> Development Advice & Evaluation
              </div>
              <div class="relative h-4">
                <span class="absolute w-25 border border-[#C88D75] top-[-2px] left-0"></span>
                <span
                  class="block w-full border-t-1 border-[#ECECEC] dark:border-gray-100/20"
                ></span>
              </div>
              <div class="grid grid-cols-2 gap-x-3">
                <div class="flex-1 h-[250px] bg-[#FAF2EF] rounded p-4 dark:bg-[#292929]">
                  <div class="fx-cer gap-2 text-3.5 font-400 dark:text-white">
                    <span
                      class="bg-[#DFDEDA] w-6 h-6 fx-cer justify-center rounded-1 dark:bg-[#433F3D]"
                      ><SvgIcon name="clipboard"
                    /></span>
                    Past Evaluation
                  </div>
                  <div
                    class="text-3.5 leading-6 text-[#464646] dark:text-[#C6C6C6] font-400 mt-2 leading-6 line-clamp-8"
                  >
                    {{ linkedinData?.profile_data?.career?.development_advice?.past_evaluation || 'Past evaluation not available.' }}
                  </div>
                </div>
                <div class="flex-1 h-[250px] bg-[#FAF2EF] rounded p-4 dark:bg-[#292929]">
                  <div class="fx-cer gap-2 text-3.5 font-400">
                    <span
                      class="bg-[#DFDEDA] w-6 h-6 fx-cer justify-center rounded-1 dark:bg-[#433F3D]"
                      ><SvgIcon name="advice"
                    /></span>
                    Future Advice
                  </div>
                  <div
                    class="text-3.5 leading-6 text-[#464646] dark:text-[#C6C6C6] font-400 mt-2 leading-6 line-clamp-8"
                  >
                    {{ linkedinData?.profile_data?.career?.development_advice?.future_advice || 'Future advice not available.' }}
                  </div>
                </div>
              </div>
            </div>
          </ReportCard>

          <!-- Estimated Salary  -->
          <ReportCard
            icon="wallet"
            :title="'Estimated Salary'"
            :card-id="'Estimated-Salary'"
            :x="-50"
            @share-popup="showPopup = true"
          >
            <div class="fx-cer flex-col justify-center">
              <div class="text-11 text-black font-600 dark:text-white">
                {{ linkedinData?.profile_data?.money_analysis?.estimated_salary
                   ? formatSalaryDisplay(linkedinData.profile_data.money_analysis.estimated_salary)
                   : 'Not Available' }}
              </div>
              <div class="text-[#7c7c7c] text-3.5 font-400 dark:text-[#7A7A7A]">
                Estimated Annual Earnings
              </div>
            </div>
            <div
              class="w-full h-[370px] rounded bg-[#FAF2EF] text-[#464646] dark:text-[#C6C6C6] text-3.5 font-400 p-4 mt-4 dark:bg-[#292929]"
            >
              {{ linkedinData?.profile_data?.money_analysis?.explanation || 'Salary analysis not available.' }}
            </div>
          </ReportCard>

          <!-- Life & Well-being  -->
          <ReportCard
            icon="healthcare"
            :title="'Life & Well-being'"
            :card-id="'Life-Well-being'"
            :x="-50"
            @share-popup="showPopup = true"
          >
            <div class="text-3.5 text-[#2C2C2C] font-700 dark:text-white mb-3">Life suggestion</div>
            <div class="relative h-4">
              <span class="absolute w-25 border border-[#C88D75] top-[-2px] left-0"></span>
              <span class="block w-full border-t-1 border-[#ECECEC] dark:border-gray-100/20"></span>
            </div>
            <div class="text-[#464646] text-3.5 font-400 leading-6 dark:text-gray-300">
              {{ linkedinData?.profile_data?.life_well_being?.life_suggestion?.advice || 'Life suggestion advice not available.' }}            </div>
            <div class="fx-cer justify-between gap-3.5 mb-4 mt-3">
              <div
                v-for="action in (linkedinData?.profile_data?.life_well_being?.life_suggestion?.actions || []).slice(0, 3)"
                :key="action.phrase"
                class="w-[175px] h-[85px] bg-[#F6F2F1] dark:bg-[#292929] fx-cer flex-col gap-2 justify-center rounded text-center"
              >
                <span>{{ action.emoji }}</span>
                <span class="text-3.5 text-[#3C3C3C] dark:text-[#C6C6C6] dark:text-gray-300"
                  >{{ action.phrase }}</span
                >
              </div>
              <!-- 如果 life_suggestion actions 少于 3 个，用默认的填充 -->
              <div
                v-for="n in Math.max(0, 3 - (linkedinData?.profile_data?.life_well_being?.life_suggestion?.actions?.length || 0))"
                :key="`life-default-${n}`"
                class="w-[175px] h-[85px] bg-[#F6F2F1] dark:bg-[#292929] fx-cer flex-col gap-2 justify-center rounded text-center"
              >
                <span>🏀</span>
                <span class="text-3.5 text-[#3C3C3C] dark:text-[#C6C6C6] dark:text-gray-300"
                  >Stay Active</span
                >
              </div>
            </div>
            <div class="text-3.5 text-[#2C2C2C] font-700 dark:text-white mb-3">Health</div>
            <div class="relative h-4">
              <span class="absolute w-25 border border-[#C88D75] top-[-2px] left-0"></span>
              <span class="block w-full border-t-1 border-[#ECECEC] dark:border-gray-100/20"></span>
            </div>
            <div class="text-[#464646] text-3.5 font-400 leading-6 dark:text-gray-300">
              {{ linkedinData?.profile_data?.life_well_being?.health?.advice || 'Health advice not available.' }}
            </div>
            <div class="fx-cer justify-between gap-3.5 mb-4 mt-3">
              <div
                v-for="action in (linkedinData?.profile_data?.life_well_being?.health?.actions || []).slice(0, 3)"
                :key="action.phrase"
                class="w-[175px] h-[85px] bg-[#F6F2F1] dark:bg-[#292929] fx-cer flex-col gap-2 justify-center rounded text-center"
              >
                <span>{{ action.emoji }}</span>
                <span class="text-3.5 text-[#3C3C3C] dark:text-[#C6C6C6] dark:text-gray-300"
                  >{{ action.phrase }}</span
                >
              </div>
              <!-- 如果 health actions 少于 3 个，用默认的填充 -->
              <div
                v-for="n in Math.max(0, 3 - (linkedinData?.profile_data?.life_well_being?.health?.actions?.length || 0))"
                :key="`health-default-${n}`"
                class="w-[175px] h-[85px] bg-[#F6F2F1] dark:bg-[#292929] fx-cer flex-col gap-2 justify-center rounded text-center"
              >
                <span>💪</span>
                <span class="text-3.5 text-[#3C3C3C] dark:text-[#C6C6C6] dark:text-gray-300"
                  >Stay Healthy</span
                >
              </div>
            </div>
          </ReportCard>

          <!-- Colleagues' View of You -->
          <ReportCard
            icon="colleagues"
            :title="'Colleagues’ View of You'"
            :card-id="'Colleagues-View-of-You'"
            @share-popup="showPopup = true"
          >
            <div class="fx-cer flex-col gap-5 h-full">
              <div class="p-4 bg-[#FFFBF2] flex-1 rounded dark:bg-[#272622]">
                <div
                  class="text-3.5 font-700 text-[#2C2C2C] leading-6 p-3 fx-cer gap-2 dark:text-white"
                >
                  <SvgIcon
                    name="idea2"
                    class-name="w-9! h-9! p-2! rounded-1.5 bg-[#FFF3CF] dark:bg-[#5B5851]"
                  />
                  Highlights
                </div>
                <div class="relative h-4">
                  <span class="absolute w-25 border border-[#DECA8F] top-[-2px] left-0"></span>
                  <span
                    class="block w-full border-t-1 border-[#ECECEC] dark:border-gray-100/20"
                  ></span>
                </div>
                <div class="text-3 font-400 text-3.5 text-[#464646] dark:text-[#7A7A7A]">
                  <div v-for="highlight in linkedinData?.profile_data?.colleagues_view?.highlights || []" :key="highlight" class="mb-2">
                    {{ highlight }}
                  </div>
                  <div v-if="!linkedinData?.profile_data?.colleagues_view?.highlights?.length">
                    No colleague highlights available.
                  </div>
                </div>
              </div>
              <div class="p-4 bg-[#FAF2EF] flex-1 rounded dark:bg-[#24201E]">
                <div
                  class="text-3.5 font-700 text-[#2C2C2C] leading-6 p-3 fx-cer gap-2 dark:text-white"
                >
                  <SvgIcon
                    name="growth3"
                    class-name="w-9! h-9! p-2! rounded-1.5 bg-[#F4D9CE] dark:bg-[#433F3D]"
                  />
                  Areas for Improvement
                </div>
                <div class="relative h-4">
                  <span class="absolute w-25 border border-[#C88D75] top-[-2px] left-0"></span>
                  <span
                    class="block w-full border-t-1 border-[#ECECEC] dark:border-gray-100/20"
                  ></span>
                </div>
                <div class="text-3 font-400 text-3.5 text-[#464646] dark:text-[#7A7A7A]">
                  <div v-for="improvement in linkedinData?.profile_data?.colleagues_view?.areas_for_improvement || []" :key="improvement" class="mb-2">
                    {{ improvement }}
                  </div>
                  <div v-if="!linkedinData?.profile_data?.colleagues_view?.areas_for_improvement?.length">
                    No improvement areas available.
                  </div>
                </div>
              </div>
            </div>
          </ReportCard>
        </div>

        <!-- Roast -->
        <ReportCard icon="mail" :title="'Roast'" :card-id="'roast'" @share-popup="showPopup = true">
          <div
            class="h-full bg-special-softPeach dark:bg-[#292929] p-4 text-15px text-gray-1000 leading-7"
            style="border-radius: 4px"
          >
            {{ parsedRoast }}
          </div>
        </ReportCard>

        <!-- I'm Creator Card -->
        <motion.div
          class="creator bg-gray-100 dark:bg-gray-800 rounded-4!"
          :initial="{ opacity: 0 }"
          :in-view="{ opacity: 1 }"
          :transition="{ duration: 1, delay: 0.5 }"
          :in-view-options="{ once: true }"
        >
          <div class="text-42px font-bold pl-20 whitespace-nowrap max-w-100">I'm Creator</div>
          <div class="max-w-200 text-[#433D3A] dark:text-white pl-10 pr-25 line-clamp-6" style="font-family: Poppins; font-weight: 400; font-size: 16px; line-height: 30px;">
            I am a living hybrid—37 trillion cells and 100 billion transistors woven into one unfolding story.
            Life is long, and work is no longer a track to endure but a field to cultivate:
            I catch birdsong between lines of code and discover nebulae inside the rhythm of meetings.
          </div>
        </motion.div>

        <!-- Bottom Search Section -->
        <motion.div
          :initial="{ opacity: 0, y: 20 }"
          :animate="{ opacity: 1, y: 0 }"
          :transition="{ duration: 0.6, ease: 'easeInOut', delay: 0.1 }"
          class="f-cer flex-col mt-15 mb-15"
          :in-view-options="{ once: true }"
        >
          <div
            class="font-semibold text-14 max-w-155 text-center clash-semibold mt-9 mb-7.5 text-black dark:text-white"
          >
            Try with other<br />
            LinkedIn profile
          </div>
          <motion.div
            :initial="{ opacity: 0, y: 10 }"
            :animate="{ opacity: 1, y: 0 }"
            :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
            class="f-cer mt-7.5 mb-7"
          >
            <div
              class="custom-input border rounded-full bg-white border-black min-h-16 w-195 p-1 flex items-center justify-between gap-4 pl-7.5"
            >
              <SearchInput ref="searchInputRef" type="linkedin" :enable-carousel="true" @enter-search="handleSearch" />
              <ActionButton @click="handleSearch" :imgSrc="magicWand" buttonText="Analyze" />
            </div>
          </motion.div>
          <div class="text-sm text-center text-neutral-100 dark:text-white mt-7.5 mb-10">
            By clicking "Analyze," you agree to our
            <a href="/terms" target="_blank" class="text-primary-100 hover:underline"
              >Terms of Service</a
            >
          </div>
        </motion.div>

      </div>
    </template>
    <InviteCodeModal
      v-if="showInviteModal"
      :error="inviteError"
      :loading="inviteLoading"
      @close="showInviteModal = false"
      @submit="handleSubmitActivationCode"
      @waiting-list="onShowWaitingListModal"
    />
    <WaitingListModal
      v-if="showWaitingListModal"
      @close="showWaitingListModal = false"
      @back="onBackToInviteCode"
    />
    <div
      v-if="inviteSuccess"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
    >
      <div
        class="bg-white dark:bg-[#1a1a1b] rounded-2xl shadow-xl p-8 pt-7 pb-7 w-full max-w-96 relative text-center"
      >
        <div class="text-lg font-semibold text-black dark:text-white mb-6">
          Enter Invite Code
          <button
            class="absolute top-6 right-6 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors text-xl"
            @click="inviteSuccess = false"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 5L5 15M5 5L15 15"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <div class="flex justify-center mb-4">
          <div class="bg-green-500 rounded-full w-12 h-12 flex items-center justify-center mx-auto">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="16" fill="none" />
              <path
                d="M10 17.5L14 21.5L22 13.5"
                stroke="#fff"
                stroke-width="2.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        <div class="text-xl font-bold text-black dark:text-white mb-2">Verification Successful</div>
        <div class="text-base text-gray-700 dark:text-gray-300 mb-7">
          Congratulations! Your invitation code has been verified successfully
        </div>
        <button
          class="w-full h-12 bg-black dark:bg-white text-white dark:text-black rounded-lg font-semibold text-base transition hover:bg-gray-900 dark:hover:bg-gray-200"
          @click="goHome"
        >
          Ok
        </button>
      </div>
    </div>
  </div>
  <LinkedInNotFound
    :visible="!loading && !linkedinData && !isFetching && hasAttemptedFetch"
    @update:visible="router.replace('/analysis')"
  />
</template>

<script setup lang="ts">
  import { useRoute } from 'vue-router'
  import InviteCodeModal from '@/components/InviteCodeModal.vue'
  import WaitingListModal from '@/components/WaitingListModal.vue'
  import GitHubProfileCard from '@/components/GitHubProfileCard/index.vue'
  import LinkedInNotFound from '@/components/LinkedInNotFound/index.vue'

  import { ref, watch } from 'vue'
  import { submitActivationCode } from '~/api'
  import type { LinkedInData, LinkedInSSEMessage } from '~/types/linkedin'
  import SearchInput from '@/components/SearchInput/index.vue'
  import ActionButton from '@/components/ActionButton/index.vue'
  import magicWand from '@/assets/image/magic-wand.png'
  import { motion } from 'motion-v'



  definePageMeta({
    middleware: 'auth',
  })



  // 从 URL 获取用户名用于初始 SEO
  const route = useRoute()
  const userParam = route.query.user as string







  // LinkedIn 数据状态变量
  const linkedinData = ref<LinkedInData | null>(null)


  const { currentUser } = useFirebaseAuth()
  // const route = useRoute() // 已在上面声明
  const router = useRouter()
  // const userParam = route.query.user as string // 已在上面声明
  const isFetching = ref(false)
  const loading = ref(false)
  const showInviteModal = ref(false)
  const inviteError = ref('')
  const inviteLoading = ref(false)
  const inviteSuccess = ref(false)
  const showWaitingListModal = ref(false)
  const showPopup = ref(false)
  const isDark = ref(false)
  // 使用 linkedinData 替代 githubData
  const usageInfo = ref<any>(null)
  const thinking = ref<string[]>([])
  const hasAttemptedFetch = ref(false)

  const shareCardRef = ref()
  const searchInputRef = ref()

  const linkedinProfileData = computed(() => {
    if (!linkedinData.value)
      return {
        name: '',
        login: '',
        avatar: '',
        bio: '',
        description: '',
        tags: [],
      }

    // LinkedIn 数据结构适配 - 优先使用 profile_data.name，null时回退到 person_name
    const userName = linkedinData.value.profile_data?.name || linkedinData.value.person_name

    // 使用 about 字段作为简介
    const bio = linkedinData.value.profile_data?.about || ''

    // 使用新的 avatar 字段，如果没有或加载失败则回退到默认头像
    const avatar = linkedinData.value.profile_data?.avatar || '/image/avator.png'

    return {
      name: userName,
      login: linkedinData.value.linkedin_id?.replace('linkedin:', '') || '',
      avatar: avatar,
      bio: bio,
      description: '', // 不再使用 description，简介已经在 bio 中显示
      tags: linkedinData.value.profile_data?.personal_tags || [], // 使用 personal_tags 作为标签
    }
  })

  const user = computed(() => {
    if (!linkedinData.value)
      return {
        name: '',
        avatar: '',
        role: 'Professional',
        login: '',
        papers: 0,
        citations: 0,
        education: [],
        work_experience: [],
      }

    // 转换教育经历数据 - 处理复杂的嵌套结构
    const education = []
    if (linkedinData.value.profile_data?.education) {
      for (const edu of linkedinData.value.profile_data.education) {
        if (edu.breakdown && edu.subComponents && edu.subComponents.length > 0) {
          // 如果 breakdown 为 true，处理同一学校的多个学位
          // 检查 subComponents 是否有有效的结构
          for (const subComp of edu.subComponents) {
            // 如果 subComponent 有 title 或其他有用信息，使用它们
            // 否则回退到主要的 edu 信息
            const timeRange = (subComp as any).caption?.split(' · ')[0] || edu.caption || ''
            const major = (subComp as any).title || edu.subtitle || ''
            
            education.push({
              school: edu.title,
              major: major,
              time: timeRange,
              logo: edu.logo, // 学校 logo
              companyLink1: edu.companyLink1 // 学校 LinkedIn 链接
            })
          }
        } else {
          // 简单的教育经历结构（不同学校或单一学位）
          const timeRange = edu.caption || ''
          let schoolName = edu.title || ''
          let major = edu.subtitle || ''

          education.push({
            school: schoolName,
            major: major,
            time: timeRange,
            logo: edu.logo, // 学校 logo
            companyLink1: edu.companyLink1 // 学校 LinkedIn 链接
          })
        }
      }
    }

    // 转换工作经历数据 - 处理复杂的嵌套结构
    const work_experience = []
    if (linkedinData.value.profile_data?.work_experience) {
      for (const work of linkedinData.value.profile_data.work_experience) {
        if (work.breakdown && work.subComponents) {
          // 如果 breakdown 为 true，处理同一公司的多个职位
          for (const subComp of work.subComponents) {
            const timeRange = subComp.caption?.split(' · ')[0] || ''
            const [from, to] = timeRange.includes(' - ')
              ? timeRange.split(' - ')
              : [timeRange, 'Present']

            work_experience.push({
              from: from.trim(),
              to: to.trim(),
              company: work.title,
              position: subComp.title || work.subtitle,
              logo: work.logo, // 公司 logo
              companyLink1: work.companyLink1 // 公司 LinkedIn 链接
            })
          }
        } else {
          // 简单的工作经历结构（不同公司）
          const timeRange = work.caption || ''
          const [from, to] = timeRange.includes(' - ')
            ? timeRange.split(' - ')
            : [timeRange, 'Present']

          // 对于 breakdown=false 的情况，title 是职位，subtitle 包含公司名
          // subtitle 格式通常是 "公司名 · 工作类型" 或 "公司名"
          let companyName = work.subtitle || ''
          let position = work.title || ''
          
          // 从 subtitle 中提取公司名（去掉 · 后面的部分）
          if (companyName.includes(' · ')) {
            companyName = companyName.split(' · ')[0].trim()
          }

          work_experience.push({
            from: from.trim(),
            to: to.trim(),
            company: companyName,
            position: position,
            logo: work.logo, // 公司 logo
            companyLink1: work.companyLink1 // 公司 LinkedIn 链接
          })
        }
      }
    }

    // 使用新的 avatar 字段，如果没有或加载失败则回退到默认头像
    const avatar = linkedinData.value.profile_data?.avatar || '/image/avator.png'

    return {
      name: linkedinData.value.profile_data?.name || linkedinData.value.person_name,
      avatar: avatar,
      role: 'Professional',
      login: linkedinData.value.linkedin_id,
      bio: linkedinData.value.profile_data?.career?.future_development_potential || '',
      papers: work_experience.length || 0,
      citations: education.length || 0,
      education,
      work_experience,
    }
  })

  const stats = computed(() => {
    if (!linkedinData.value)
      return {
        repositories: 0,
        stars: 0,
        pullRequests: 0,
      }

    // LinkedIn 数据适配为类似 GitHub 的统计数据
    const workExperience = linkedinData.value.profile_data?.work_experience?.length || 0
    const education = linkedinData.value.profile_data?.education?.length || 0
    const skills = Object.values(linkedinData.value.profile_data?.skills || {}).flat().length || 0

    return {
      repositories: workExperience, // 工作经历数量
      stars: education, // 教育经历数量
      pullRequests: skills, // 技能数量
    }
  })

  const roleModel = computed(() => {
    if (!linkedinData.value || !linkedinData.value.profile_data?.role_model)
      return {
        name: 'Unknown',
        avatar: '/image/avator.png',
        title: 'Professional',
        achievement: '',
      }

    const roleModelData = linkedinData.value.profile_data.role_model

    return {
      name: roleModelData.name || 'Unknown',
      avatar: roleModelData.photo_url || '/image/avator.png',
      title: roleModelData.position || 'Professional',
      achievement: roleModelData.achievement || roleModelData.similarity_reason || 'LinkedIn Professional',
    }
  })

  const items = computed(() => {
    if (!linkedinData.value) return []
    const workExp = linkedinData.value.profile_data?.work_experience?.length || 0
    const education = linkedinData.value.profile_data?.education?.length || 0
    const skills = Object.values(linkedinData.value.profile_data?.skills || {}).flat().length || 0

    return [
      { label: 'Work Experience', value: workExp },
      { label: 'Education', value: education },
      { label: 'Skills', value: skills },
    ]
  })

  // 解析 roast 数据的 computed 属性
  const parsedRoast = computed(() => {
    if (!linkedinData.value?.profile_data?.roast) {
      return 'No roast available'
    }

    try {
      let roastContent = linkedinData.value.profile_data.roast

      // 首先提取 markdown 代码块中的 JSON 内容
      // 匹配 ```json ... ``` 格式
      const markdownMatch = roastContent.match(/```json\s*([\s\S]*?)\s*```/)
      if (markdownMatch) {
        roastContent = markdownMatch[1].trim()
      }

      // 然后解析 JSON
      const roastData = JSON.parse(roastContent)
      return roastData.roast || 'No roast available'
    } catch (e) {
      console.warn('Failed to parse roast data:', e)

      // 如果解析失败，尝试直接提取文本内容
      let fallbackContent = linkedinData.value.profile_data.roast

      // 尝试从 markdown 中提取纯文本
      const textMatch = fallbackContent.match(/```json\s*{\s*"roast":\s*"([^"]*?)"\s*}\s*```/)
      if (textMatch) {
        return textMatch[1]
      }

      // 最后回退到原始数据
      return fallbackContent || 'No roast available'
    }
  })

  // 格式化数字，超过1K时显示为K单位，超过1M时显示为M单位
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'K'
    }
    return num.toString()
  }

  // 格式化薪资显示，将 - 替换为 ~ 并应用数字格式化
  const formatSalaryDisplay = (salaryRange: string | number): string => {
    if (!salaryRange) return ''

    // 如果是数字，直接格式化
    if (typeof salaryRange === 'number') {
      return '$' + formatNumber(salaryRange)
    }

    // 先替换分隔符
    let formatted = salaryRange.replace(/-/g, '~')

    // 匹配薪资数字并格式化
    // 匹配格式如: "50000~80000 USD/month", "$50000~80000/month", "50000 USD/month"
    formatted = formatted.replace(/(\d+(?:,\d{3})*)/g, (match) => {
      const num = parseInt(match.replace(/,/g, ''))
      return formatNumber(num)
    })

    return formatted
  }

  // Watch for route changes to reset state
  watch(
    () => route.query.user,
    (newUser) => {
      // Reset invite success state when user parameter changes
      inviteSuccess.value = false
    }
  )

  onMounted(() => {
    // Reset invite success state on mount to prevent stale state
    inviteSuccess.value = false

    // 安全地检测主题模式
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })

    // Start LinkedIn analysis if userParam exists
    if (userParam) {
      analyzeLinkedInUser(userParam)
    }
  })

  // LinkedIn API integration functions with SSE
  const analyzeLinkedInUser = async (username: string) => {
    if (!username.trim()) return

    loading.value = true
    isFetching.value = true
    hasAttemptedFetch.value = true
    thinking.value = [`Analyzing LinkedIn profile for ${username}...`]

    console.log('Starting LinkedIn analysis for:', username)

    try {
      await connectLinkedIn(username.trim())
    } catch (error: any) {
      console.error('Error analyzing LinkedIn user:', error)

      // Handle rate limit errors
      if (error.message?.includes('usage limit') || error.message?.includes('429')) {
        showInviteModal.value = true
        inviteError.value = ''
        inviteLoading.value = false
        return
      }

      // Show error to user
    } finally {
      loading.value = false
      isFetching.value = false
      thinking.value = []
      console.log('Analysis completed')
    }
  }

  // LinkedIn SSE 连接函数
  const connectLinkedIn = async (content: string) => {
    try {
      const response = await fetch('https://api.dinq.io/api/linkedin/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Userid': currentUser.value?.uid || ''
        },
        body: JSON.stringify({ content })
      })

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      const readStream = async () => {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const messages = buffer.split('\n\n')
          buffer = messages.pop() || ''

          for (const message of messages) {
            try {
              let jsonData = ''
              message.split('\n').forEach(line => {
                if (line.startsWith('data:')) {
                  jsonData = line.substring(5).trim()
                }
              })

              if (jsonData) {
                const data = JSON.parse(jsonData)
                processLinkedInMessage(data)
              }
            } catch (e) {
              console.error('Error parsing SSE message:', e)
            }
          }
        }
      }

      await readStream()
    } catch (err) {
      loading.value = false
      console.error('LinkedIn connection error:', err)
      throw err
    }
  }

  // LinkedIn 消息处理函数
  const processLinkedInMessage = (data: LinkedInSSEMessage) => {
    console.log('LinkedIn message:', data)

    switch (data.type) {
      case 'start':
        thinking.value = [data.message || 'Starting analysis...']
        break

      case 'progress':
        // Add different progress information based on steps
        const stepMessages: Record<string, string> = {
          'searching': 'Searching LinkedIn profile...',
          'url_found': 'Profile found, getting detailed information...',
          'checking_cache': 'Checking cached data...',
          'validating_cache': 'Validating cached data...',
          'complete': 'Analysis completed!'
        }
        const stepMessage = stepMessages[data.step || ''] || data.message || 'Processing...'
        thinking.value = [...thinking.value, stepMessage]
        break

      case 'success':
        // 保存 LinkedIn 数据
        if (data.data) {
          linkedinData.value = data.data
          usageInfo.value = { /* 可以添加使用信息 */ }
        }
        loading.value = false
        break

      case 'end':
        loading.value = false
        break

      case 'error':
        // 处理错误情况
        console.error('LinkedIn analysis error:', data.error)
        loading.value = false
        // 可以显示错误提示给用户
        if (data.error?.message?.includes('usage limit') || data.error?.message?.includes('429')) {
          showInviteModal.value = true
          inviteError.value = ''
          inviteLoading.value = false
        }
        break

      default:
        // 处理其他类型的消息
        if (data.message) {
          thinking.value = [...thinking.value, data.message]
        }
    }
  }





  // Invite code handling functions
  function onShowWaitingListModal() {
    showInviteModal.value = false
    showWaitingListModal.value = true
  }

  function onBackToInviteCode() {
    showWaitingListModal.value = false
    showInviteModal.value = true
  }

  async function handleSubmitActivationCode(code: string) {
    inviteLoading.value = true
    inviteError.value = ''
    try {
      const res = await submitActivationCode(
        '/api/activation-codes/use',
        { code },
        { headers: { Userid: currentUser.value?.uid || '' } }
      )
      if (res.data?.success) {
        showInviteModal.value = false
        inviteSuccess.value = true
        setTimeout(() => {
          inviteSuccess.value = false
        }, 2000)
      } else {
        inviteError.value = 'Invalid invite code. Please check and try again.'
      }
    } catch (e) {
      inviteError.value = 'Invalid invite code. Please check and try again.'
    } finally {
      inviteLoading.value = false
    }
  }

  function goHome() {
    router.replace('/analysis')
  }

  // 搜索处理函数
  const handleSearch = (query?: string) => {
    const { $emitter } = useNuxtApp()
    let search = query || searchInputRef.value?.searchValue

    // 如果没有搜索内容，尝试从轮播placeholder获取
    if (!search?.trim() && searchInputRef.value) {
      // 获取当前显示的placeholder
      const currentPlaceholder = searchInputRef.value.currentPlaceholder
      if (currentPlaceholder) {
        // 判断是否为提示文本
        const isHelpText = (text: string) => {
          return text.includes('Enter ') || text.includes('Paste any ') ||
                 text === 'LinkedIn Username' || text === 'LinkedIn profile link'
        }

        // 如果是提示文本，不执行搜索
        if (isHelpText(currentPlaceholder)) {
          // 如果没有登录，提示登录
          if (!currentUser.value) {
            $emitter.emit('auth')
          }
          return
        }

        // 直接使用完整内容（URL或人名）
        search = currentPlaceholder.trim()
      }
    }

    if (!search?.trim()) {
      // 如果没有登录，提示登录
      if (!currentUser.value) {
        $emitter.emit('auth')
      }
      return
    }

    // 检查用户是否登录
    if (!currentUser.value) {
      $emitter.emit('auth')
      return
    }

    // Update URL with new user parameter
    router.replace({
      path: '/linkedin',
      query: { user: search.trim() },
    })

    // Reset data and start new analysis
    linkedinData.value = null
    hasAttemptedFetch.value = false
    
    // Scroll to top smoothly (client-side only)
    if (typeof window !== 'undefined') {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
    
    analyzeLinkedInUser(search.trim())
  }
</script>

<style scoped lang="scss">
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .creator {
    background-image: url('~/assets/image/technical.png');
    background-size: 100% 100%;
    background-position: center;
    height: 13.25rem; /* h-53 */
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 155px; /* gap-155px */
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .dark .creator {
    background-image: url('~/assets/image/technicaldark.png');
    background-size: 100% 100%;
    background-position: center;
    height: 13.25rem; /* h-53 */
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 155px; /* gap-155px */
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  // 文本行数限制样式
  .line-clamp-5 {
    display: -webkit-box;
    -webkit-line-clamp: 5;
    line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  .line-clamp-6 {
    display: -webkit-box;
    -webkit-line-clamp: 6;
    line-clamp: 6;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  .line-clamp-8 {
    display: -webkit-box;
    -webkit-line-clamp: 8;
    line-clamp: 8;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }
</style>
