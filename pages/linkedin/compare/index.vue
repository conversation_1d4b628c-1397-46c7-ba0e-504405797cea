<template>
  <div class="min-h-screen flex items-center justify-center">
    <div class="text-lg text-gray-600">Redirecting...</div>
  </div>
</template>

<script setup lang="ts">
  // 重定向到新的路径
  const route = useRoute()
  const router = useRouter()
  
  // 立即重定向到新路径
  if (import.meta.client) {
    const newPath = '/github_compare'
    const query = route.query
    router.replace({ path: newPath, query })
  }

  // 服务端渲染时的重定向
  if (import.meta.server) {
    const query = route.query
    const queryString = new URLSearchParams(query as Record<string, string>).toString()
    const redirectUrl = `/github_compare${queryString ? '?' + queryString : ''}`
    
    await navigateTo(redirectUrl, { 
      redirectCode: 301 // 永久重定向
    })
  }
</script>
