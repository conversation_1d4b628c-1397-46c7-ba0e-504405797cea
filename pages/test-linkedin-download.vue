<template>
  <div class="min-h-screen bg-gray-100 dark:bg-gray-900 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
        LinkedIn Share Card Download Test
      </h1>
      
      <div class="text-center mb-8">
        <button
          @click="showPopup = true"
          class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
        >
          Open LinkedIn Share Card
        </button>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg mb-8">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">测试说明</h2>
        <ul class="space-y-2 text-gray-700 dark:text-gray-300">
          <li>• 点击上方按钮打开 LinkedIn 分享卡片</li>
          <li>• 在卡片右下角可以看到新增的 "Download" 按钮</li>
          <li>• 点击 Download 按钮可以下载卡片为 PNG 图片</li>
          <li>• 下载时会隐藏操作按钮，确保截图干净</li>
          <li>• 支持深色和浅色主题</li>
        </ul>
      </div>

      <!-- Simple Test Component -->
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white">简单下载测试</h2>
        <TestDownload />
      </div>

      <!-- LinkedIn Share Card Modal -->
      <Teleport to="body">
        <ShareCardLinkedin
          :show="showPopup"
          :user="mockUser"
          :linkedin-data="mockLinkedinData"
          :is-dark="isDark"
          @close="showPopup = false"
        />
      </Teleport>

      <!-- Theme Toggle -->
      <div class="fixed top-4 right-4">
        <button
          @click="isDark = !isDark"
          class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg transition-colors"
        >
          {{ isDark ? '🌞 Light' : '🌙 Dark' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import ShareCardLinkedin from '~/components/ShareCardLinkedin/index.vue'
import TestDownload from '~/components/TestDownload.vue'

const showPopup = ref(false)
const isDark = ref(false)

// 监听主题变化
watch(isDark, (newValue) => {
  if (process.client) {
    if (newValue) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }
})

// Mock 用户数据
const mockUser = {
  name: 'Trevor Darrell',
  avatar: '/image/avator.png',
  role: 'AI Researcher',
  work_experience: [
    {
      from: '2020',
      to: 'Present',
      company: 'UC Berkeley',
      position: 'Professor of Computer Science',
      logo: '/image/berkeley-logo.png'
    }
  ]
}

// Mock LinkedIn 数据
const mockLinkedinData = {
  profile_data: {
    career: {
      future_development_potential: 'Strong potential for advancement in AI research and academic leadership roles.',
      development_advice: {
        past_evaluation: 'Excellent track record in computer vision and machine learning research with significant industry impact.',
        future_advice: 'Continue building interdisciplinary collaborations and expand into emerging AI domains.'
      }
    },
    life_well_being: {
      life_suggestion: {
        advice: 'Maintain work-life balance while pursuing cutting-edge research opportunities.',
        actions: [
          { emoji: '🧘', phrase: 'Meditation' },
          { emoji: '🏃', phrase: 'Exercise' },
          { emoji: '📚', phrase: 'Reading' }
        ]
      },
      health: {
        advice: 'Regular exercise and stress management are key for sustained productivity.',
        actions: [
          { emoji: '💪', phrase: 'Gym' },
          { emoji: '🥗', phrase: 'Healthy Diet' },
          { emoji: '😴', phrase: 'Sleep' }
        ]
      }
    },
    money_analysis: {
      estimated_salary: '$180K - $250K USD/year',
      explanation: 'Based on academic position and research impact in AI field.'
    },
    role_model: {
      name: 'Fei-Fei Li',
      institution: 'Stanford University',
      position: 'Professor of Computer Science',
      photo_url: '/image/avator.png',
      achievement: 'Pioneer in computer vision and AI ethics, former Chief Scientist at Google Cloud.',
      similarity_reason: 'Both are leading researchers in computer vision and AI with strong academic and industry connections.'
    }
  }
}
</script>

<style scoped>
/* 确保深色模式样式正确应用 */
.dark {
  color-scheme: dark;
}
</style>
