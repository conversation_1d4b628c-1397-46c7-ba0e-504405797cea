<template>
  <div class="px-30 pt-7.5 h-full">
    <!-- Test Page Title -->
    <div class="text-center mb-10">
      <h1 class="text-4xl font-bold text-black dark:text-white mb-4">LinkedIn API Test Page</h1>
      <p class="text-gray-600 dark:text-gray-400">Enter LinkedIn username or URL for analysis testing</p>
    </div>

    <!-- 输入表单 -->
    <div class="max-w-2xl mx-auto mb-10">
      <div class="bg-white dark:bg-[#141415] rounded-2xl p-8 shadow-lg">
        <div class="space-y-6">
          <!-- Login Status Display -->
          <div class="flex items-center justify-between p-3 rounded-lg"
               :class="currentUser?.uid ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'">
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full mr-2"
                   :class="currentUser?.uid ? 'bg-green-500' : 'bg-yellow-500'"></div>
              <span class="text-sm font-medium"
                    :class="currentUser?.uid ? 'text-green-800 dark:text-green-200' : 'text-yellow-800 dark:text-yellow-200'">
                {{ currentUser?.uid ? 'Logged In' : 'Not Logged In' }}
              </span>
            </div>
            <span class="text-xs"
                  :class="currentUser?.uid ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'">
              {{ currentUser?.uid ? 'Ready to analyze' : 'Please login first' }}
            </span>
          </div>

          <!-- Input Field -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              LinkedIn Username or URL
            </label>
            <input
              v-model="inputValue"
              type="text"
              placeholder="e.g.: john-doe or https://linkedin.com/in/john-doe"
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
                     bg-white dark:bg-[#1a1a1b] text-black dark:text-white
                     focus:ring-2 focus:ring-blue-500 focus:border-transparent
                     placeholder-gray-400 dark:placeholder-gray-500"
              @keyup.enter="startAnalysis"
            />
          </div>

          <!-- Start Analysis Button -->
          <button
            @click="startAnalysis"
            :disabled="!inputValue.trim() || loading || !currentUser?.uid"
            class="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400
                   text-white font-medium rounded-lg transition-colors duration-200
                   disabled:cursor-not-allowed"
          >
            {{ loading ? 'Analyzing...' : !currentUser?.uid ? 'Please Login First' : 'Start Analysis' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading 模态框 -->
    <Loading 
      :visible="loading" 
      :data="thinking" 
      @update:visible="handleLoadingClose" 
    />

    <!-- 结果显示区域 -->
    <div v-if="(finalResult || linkedinData) && !loading" class="max-w-6xl mx-auto space-y-6">

      <!-- LinkedIn Data Overview -->
      <div v-if="linkedinData" class="bg-white dark:bg-[#141415] rounded-2xl p-8 shadow-lg">
        <h2 class="text-2xl font-bold text-black dark:text-white mb-6">LinkedIn Analysis Results</h2>

        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div class="space-y-4">
            <div>
              <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Basic Information</h3>
              <p class="text-gray-600 dark:text-gray-400">Name: {{ linkedinData.person_name }}</p>
              <p class="text-gray-600 dark:text-gray-400">LinkedIn ID: {{ linkedinData.linkedin_id }}</p>
              <a :href="linkedinData.linkedin_url" target="_blank"
                 class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                View LinkedIn Profile →
              </a>
            </div>
          </div>

          <div v-if="linkedinData.profile_data?.money_analysis" class="space-y-4">
            <div>
              <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Salary Analysis</h3>
              <p class="text-gray-600 dark:text-gray-400">
                Estimated Salary: {{ linkedinData.profile_data.money_analysis.estimated_salary }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">
                {{ linkedinData.profile_data.money_analysis.explanation }}
              </p>
            </div>
          </div>
        </div>

        <!-- Analyze Again Button -->
        <div class="text-center">
          <button
            @click="resetAnalysis"
            class="py-2 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg
                   transition-colors duration-200"
          >
            Analyze Again
          </button>
        </div>
      </div>

      <!-- Raw Data Display -->
      <div class="bg-white dark:bg-[#141415] rounded-2xl p-8 shadow-lg">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-bold text-black dark:text-white">Raw Data (Debug)</h2>
          <button
            @click="copyToClipboard"
            class="py-1 px-3 bg-gray-500 hover:bg-gray-600 text-white text-sm rounded
                   transition-colors duration-200"
          >
            Copy Data
          </button>
        </div>

        <!-- JSON Data -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 overflow-auto max-h-96">
          <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ finalResult }}</pre>
        </div>
      </div>

      <!-- Raw Message Stream -->
      <div v-if="rawMessages.length > 0" class="bg-white dark:bg-[#141415] rounded-2xl p-8 shadow-lg">
        <h2 class="text-xl font-bold text-black dark:text-white mb-6">Message Stream (Debug)</h2>

        <div class="space-y-2 max-h-64 overflow-y-auto">
          <div v-for="(message, index) in rawMessages" :key="index"
               class="bg-gray-50 dark:bg-gray-800 rounded p-3 text-sm">
            <div class="text-gray-500 dark:text-gray-400 text-xs mb-1">Message {{ index + 1 }}:</div>
            <pre class="text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ message }}</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Display -->
    <div v-if="error && !loading" class="max-w-2xl mx-auto">
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-5 h-5 text-red-400">⚠️</div>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
              Analysis Error
            </h3>
            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
              {{ error.message }}
            </div>
          </div>
        </div>

        <!-- Retry Button -->
        <div class="mt-4">
          <button
            @click="startAnalysis"
            class="py-2 px-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded
                   transition-colors duration-200"
          >
            Retry
          </button>
        </div>
      </div>
    </div>

    <!-- Usage Instructions -->
    <div class="max-w-2xl mx-auto mt-10">
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <h3 class="text-lg font-medium text-blue-800 dark:text-blue-200 mb-3">Usage Instructions</h3>
        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-2">
          <li>• Supports LinkedIn username input (e.g.: john-doe)</li>
          <li>• Supports full LinkedIn URL input (e.g.: https://linkedin.com/in/john-doe)</li>
          <li>• Real-time progress will be displayed during analysis</li>
          <li>• Detailed results will be shown after analysis completion</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useFirebaseAuth } from '~/composables/useFirebaseAuth'
import Loading from '~/components/Loading/index.vue'

// 页面状态
const inputValue = ref('')
const finalResult = ref('')
const error = ref<Error | null>(null)

// 使用 Firebase 认证
const { currentUser } = useFirebaseAuth()

// LinkedIn 特定的状态
const loading = ref(false)
const thinking = ref<string[]>([])
const linkedinData = ref<any>(null)
const rawMessages = ref<string[]>([])  // 存储原始消息用于调试

// 自定义 LinkedIn SSE 连接函数
const connectLinkedIn = async (content: string) => {
  try {
    loading.value = true

    const response = await fetch('https://api.dinq.io/api/linkedin/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Userid': currentUser.value?.uid || ''
      },
      body: JSON.stringify({ content })
    })

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('Failed to get response reader')
    }

    const decoder = new TextDecoder()
    let buffer = ''

    const readStream = async () => {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const messages = buffer.split('\n\n')
        buffer = messages.pop() || ''

        for (const message of messages) {
          try {
            let jsonData = ''
            message.split('\n').forEach(line => {
              if (line.startsWith('data:')) {
                jsonData = line.substring(5).trim()
              }
            })

            if (jsonData) {
              const data = JSON.parse(jsonData)
              processLinkedInMessage(data)
            }
          } catch (e) {
            console.error('Error parsing SSE message:', e)
          }
        }
      }
    }

    await readStream()
  } catch (err) {
    error.value = err as Error
    loading.value = false
    console.error('LinkedIn connection error:', err)
  }
}

// 自定义 LinkedIn 消息处理函数
const processLinkedInMessage = (data: any) => {
  // 记录原始消息用于调试
  rawMessages.value.push(JSON.stringify(data, null, 2))

  console.log('LinkedIn message:', data)

  switch (data.type) {
    case 'start':
      thinking.value = [data.message]
      break

    case 'progress':
      // Add different progress information based on steps
      const stepMessages = {
        'searching': 'Searching LinkedIn profile...',
        'url_found': 'Profile found, getting detailed information...',
        'checking_cache': 'Checking cached data...',
        'validating_cache': 'Validating cached data...',
        'complete': 'Analysis completed!'
      }
      const stepMessage = stepMessages[data.step] || data.message
      thinking.value = [...thinking.value, stepMessage]
      break

    case 'success':
      // 保存 LinkedIn 数据
      linkedinData.value = data.data
      finalResult.value = JSON.stringify(data.data, null, 2)
      loading.value = false
      break

    case 'end':
      loading.value = false
      break

    default:
      // 处理其他类型的消息
      if (data.message) {
        thinking.value = [...thinking.value, data.message]
      }
  }
}

// 开始分析
const startAnalysis = async () => {
  if (!inputValue.value.trim()) {
    return
  }

  // Check if user is logged in
  if (!currentUser.value?.uid) {
    error.value = new Error('Please login first before analysis')
    return
  }

  // 重置状态
  finalResult.value = ''
  error.value = null
  thinking.value = []
  linkedinData.value = null
  rawMessages.value = []

  try {
    // 使用自定义的 LinkedIn 连接函数
    await connectLinkedIn(inputValue.value.trim())
  } catch (err) {
    error.value = err as Error
    console.error('LinkedIn analysis error:', err)
  }
}

// 处理 Loading 模态框关闭
const handleLoadingClose = () => {
  // 如果用户手动关闭 Loading，停止当前分析
  loading.value = false
}

// 重置分析
const resetAnalysis = () => {
  inputValue.value = ''
  finalResult.value = ''
  error.value = null
  thinking.value = []
  linkedinData.value = null
  rawMessages.value = []
}

// Copy to clipboard
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(finalResult.value)
    // Simple notification
    console.log('Data copied to clipboard')
  } catch (err) {
    console.error('Copy failed:', err)
  }
}

// 页面元数据
useHead({
  title: 'LinkedIn API 测试 - DINQ',
  meta: [
    { name: 'description', content: 'LinkedIn API 分析测试页面' }
  ]
})
</script>

<style scoped>
/* 自定义样式 */
.prose {
  max-width: none;
}

.prose pre {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.dark .prose pre {
  background-color: #1a1a1b;
}
</style>
