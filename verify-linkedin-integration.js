/**
 * LinkedIn 接口对接验证脚本
 * 验证代码结构、类型定义和组件集成的正确性
 */

import fs from 'fs'
import path from 'path'

/**
 * 验证文件是否存在
 */
function verifyFileExists(filePath, description) {
  const exists = fs.existsSync(filePath)
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`)
  return exists
}

/**
 * 验证文件内容包含特定字符串
 */
function verifyFileContains(filePath, searchStrings, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const results = searchStrings.map(str => ({
      string: str,
      found: content.includes(str)
    }))
    
    const allFound = results.every(r => r.found)
    console.log(`${allFound ? '✅' : '❌'} ${description}`)
    
    if (!allFound) {
      results.filter(r => !r.found).forEach(r => {
        console.log(`   ❌ Missing: ${r.string}`)
      })
    }
    
    return allFound
  } catch (error) {
    console.log(`❌ ${description}: Error reading file - ${error.message}`)
    return false
  }
}

/**
 * 验证 TypeScript 类型定义
 */
function verifyTypeDefinitions() {
  console.log('\n🔍 Verifying TypeScript type definitions...')
  
  const typeFile = 'types/linkedin.ts'
  const requiredTypes = [
    'LinkedInAnalysisResponse',
    'LinkedInData',
    'LinkedInProfileData',
    'LinkedInRoleModel',
    'MoneyAnalysis',
    'LinkedInSkills',
    'ColleaguesView',
    'LinkedInCareer',
    'LifeWellBeing',
    'WorkExperience',
    'Education',
    'LinkedInSSEMessage'
  ]
  
  let success = verifyFileExists(typeFile, 'LinkedIn TypeScript definitions')
  
  if (success) {
    success = verifyFileContains(
      typeFile,
      requiredTypes,
      'Required TypeScript interfaces'
    )
  }
  
  return success
}

/**
 * 验证主页面文件的修改
 */
function verifyMainPageModifications() {
  console.log('\n🔍 Verifying main page modifications...')
  
  const pageFile = 'pages/linkedin/index.vue'
  const requiredChanges = [
    'analyzeLinkedInUser',
    'connectLinkedIn',
    'processLinkedInMessage',
    'linkedinData',
    'LinkedInData',
    'LinkedInSSEMessage',
    'extractLinkedInUsername',
    'linkedin.com/in/'
  ]
  
  let success = verifyFileExists(pageFile, 'LinkedIn main page')
  
  if (success) {
    success = verifyFileContains(
      pageFile,
      requiredChanges,
      'LinkedIn-specific modifications'
    )
  }
  
  return success
}

/**
 * 验证组件修改
 */
function verifyComponentModifications() {
  console.log('\n🔍 Verifying component modifications...')
  
  const shareCardFile = 'components/ShareCardLinkedin/index.vue'
  const notFoundFile = 'components/GitHubNotFound/index.vue'
  
  let success = true
  
  // 验证 ShareCardLinkedin 组件
  success &= verifyFileExists(shareCardFile, 'ShareCardLinkedin component')
  if (success) {
    success &= verifyFileContains(
      shareCardFile,
      ['linkedin.com/in/', 'share-card-linkedin'],
      'ShareCardLinkedin LinkedIn adaptations'
    )
  }
  
  // 验证 GitHubNotFound 组件的文本更新
  success &= verifyFileExists(notFoundFile, 'GitHubNotFound component')
  if (success) {
    success &= verifyFileContains(
      notFoundFile,
      ['LinkedIn username', 'LinkedIn link'],
      'GitHubNotFound LinkedIn text updates'
    )
  }
  
  return success
}

/**
 * 验证 API 集成
 */
function verifyAPIIntegration() {
  console.log('\n🔍 Verifying API integration...')
  
  const pageFile = 'pages/linkedin/index.vue'
  const apiChanges = [
    'https://api.dinq.io/api/linkedin/analyze',
    'POST',
    'Content-Type',
    'application/json',
    'Userid',
    'SSE',
    'EventSource'
  ]
  
  return verifyFileContains(
    pageFile,
    apiChanges.slice(0, 5), // 只检查前5个，因为SSE和EventSource可能以不同形式存在
    'API integration changes'
  )
}

/**
 * 验证数据绑定
 */
function verifyDataBinding() {
  console.log('\n🔍 Verifying data binding...')
  
  const pageFile = 'pages/linkedin/index.vue'
  const dataBindings = [
    'linkedinData?.profile_data?.role_model',
    'linkedinData?.profile_data?.money_analysis',
    'linkedinData?.profile_data?.skills',
    'linkedinData?.profile_data?.colleagues_view',
    'linkedinData?.profile_data?.career',
    'parsedRoast' // 更新为使用 computed 属性
  ]
  
  return verifyFileContains(
    pageFile,
    dataBindings,
    'LinkedIn data binding'
  )
}

/**
 * 验证测试页面存在
 */
function verifyTestPage() {
  console.log('\n🔍 Verifying test page...')
  
  const testPageFile = 'pages/linkedin-test.vue'
  const exampleDataFile = 'public/example.json'
  
  let success = verifyFileExists(testPageFile, 'LinkedIn test page')
  success &= verifyFileExists(exampleDataFile, 'Example data file')
  
  return success
}

/**
 * 运行所有验证
 */
async function runAllVerifications() {
  console.log('🚀 Starting LinkedIn integration verification...\n')
  
  const results = {
    typeDefinitions: verifyTypeDefinitions(),
    mainPageModifications: verifyMainPageModifications(),
    componentModifications: verifyComponentModifications(),
    apiIntegration: verifyAPIIntegration(),
    dataBinding: verifyDataBinding(),
    testPage: verifyTestPage()
  }
  
  console.log('\n📋 Verification Results Summary:')
  Object.entries(results).forEach(([key, value]) => {
    const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
    console.log(`${label}: ${value ? '✅ PASS' : '❌ FAIL'}`)
  })
  
  const overallSuccess = Object.values(results).every(Boolean)
  console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ ALL VERIFICATIONS PASSED' : '❌ SOME VERIFICATIONS FAILED'}`)
  
  if (overallSuccess) {
    console.log('\n🎉 LinkedIn integration appears to be correctly implemented!')
    console.log('📝 Next steps:')
    console.log('   1. Test with real LinkedIn profiles')
    console.log('   2. Verify UI/UX with actual data')
    console.log('   3. Test error handling scenarios')
    console.log('   4. Performance testing with large datasets')
  } else {
    console.log('\n⚠️ Some issues were found. Please review and fix the failed verifications.')
  }
  
  return overallSuccess
}

/**
 * 主函数
 */
async function main() {
  try {
    const success = await runAllVerifications()
    process.exit(success ? 0 : 1)
  } catch (error) {
    console.error('💥 Verification execution failed:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export {
  runAllVerifications,
  verifyTypeDefinitions,
  verifyMainPageModifications,
  verifyComponentModifications,
  verifyAPIIntegration,
  verifyDataBinding,
  verifyTestPage
}
