/**
 * LinkedIn 接口对接测试脚本
 * 验证 LinkedIn 接口对接的完整性和正确性
 */

// 测试用例配置
const TEST_CONFIG = {
  // 测试用的 LinkedIn 用户名
  testUsername: 'carriewhitea',
  // API 端点
  apiEndpoint: 'https://api.dinq.io/api/linkedin/analyze',
  // 测试超时时间 (毫秒)
  timeout: 30000
}

// 模拟用户ID (在实际测试中应该使用真实的用户ID)
const mockUserId = 'test-user-id'

/**
 * 测试 LinkedIn API 连接
 */
async function testLinkedInAPIConnection() {
  console.log('🔍 Testing LinkedIn API connection...')
  
  try {
    const response = await fetch(TEST_CONFIG.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Userid': mockUserId
      },
      body: JSON.stringify({ content: TEST_CONFIG.testUsername })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    console.log('✅ LinkedIn API connection successful')
    return true
  } catch (error) {
    console.error('❌ LinkedIn API connection failed:', error.message)
    return false
  }
}

/**
 * 测试 SSE 流式传输
 */
async function testSSEStreaming() {
  console.log('🔍 Testing SSE streaming...')
  
  return new Promise((resolve) => {
    let messageCount = 0
    let hasStartMessage = false
    let hasProgressMessage = false
    let hasSuccessMessage = false
    
    const timeout = setTimeout(() => {
      console.log('⚠️ SSE streaming test timed out')
      resolve(false)
    }, TEST_CONFIG.timeout)

    fetch(TEST_CONFIG.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Userid': mockUserId
      },
      body: JSON.stringify({ content: TEST_CONFIG.testUsername })
    })
    .then(response => {
      if (!response.body) {
        throw new Error('No response body')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      const readStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            buffer += decoder.decode(value, { stream: true })
            const messages = buffer.split('\n\n')
            buffer = messages.pop() || ''

            for (const message of messages) {
              try {
                let jsonData = ''
                message.split('\n').forEach(line => {
                  if (line.startsWith('data:')) {
                    jsonData = line.substring(5).trim()
                  }
                })

                if (jsonData) {
                  const data = JSON.parse(jsonData)
                  messageCount++
                  
                  console.log(`📨 Received message ${messageCount}:`, data.type)
                  
                  switch (data.type) {
                    case 'start':
                      hasStartMessage = true
                      break
                    case 'progress':
                      hasProgressMessage = true
                      break
                    case 'success':
                      hasSuccessMessage = true
                      // 验证数据结构
                      if (data.data && data.data.linkedin_id && data.data.profile_data) {
                        console.log('✅ SSE streaming test successful')
                        console.log(`📊 Total messages received: ${messageCount}`)
                        clearTimeout(timeout)
                        resolve(true)
                        return
                      }
                      break
                    case 'error':
                      console.error('❌ Received error message:', data.error)
                      clearTimeout(timeout)
                      resolve(false)
                      return
                  }
                }
              } catch (e) {
                console.error('Error parsing SSE message:', e)
              }
            }
          }
        } catch (error) {
          console.error('❌ SSE streaming error:', error)
          clearTimeout(timeout)
          resolve(false)
        }
      }

      readStream()
    })
    .catch(error => {
      console.error('❌ SSE streaming failed:', error)
      clearTimeout(timeout)
      resolve(false)
    })
  })
}

/**
 * 测试数据结构验证
 */
function testDataStructure(data) {
  console.log('🔍 Testing data structure...')
  
  const requiredFields = [
    'linkedin_id',
    'person_name',
    'linkedin_url',
    'profile_data'
  ]
  
  const profileDataFields = [
    'role_model',
    'money_analysis',
    'skills',
    'colleagues_view',
    'career',
    'life_well_being',
    'work_experience',
    'education',
    'about',
    'personal_tags'
  ]
  
  // 检查顶级字段
  for (const field of requiredFields) {
    if (!data[field]) {
      console.error(`❌ Missing required field: ${field}`)
      return false
    }
  }
  
  // 检查 profile_data 字段
  for (const field of profileDataFields) {
    if (!data.profile_data[field]) {
      console.warn(`⚠️ Missing profile_data field: ${field}`)
    }
  }
  
  console.log('✅ Data structure validation passed')
  return true
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 Starting LinkedIn integration tests...\n')
  
  const results = {
    apiConnection: false,
    sseStreaming: false,
    dataStructure: false
  }
  
  // 测试 API 连接
  results.apiConnection = await testLinkedInAPIConnection()
  console.log('')
  
  // 测试 SSE 流式传输
  if (results.apiConnection) {
    results.sseStreaming = await testSSEStreaming()
  } else {
    console.log('⏭️ Skipping SSE test due to API connection failure')
  }
  
  console.log('\n📋 Test Results Summary:')
  console.log(`API Connection: ${results.apiConnection ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`SSE Streaming: ${results.sseStreaming ? '✅ PASS' : '❌ FAIL'}`)
  
  const overallSuccess = results.apiConnection && results.sseStreaming
  console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)
  
  return overallSuccess
}

/**
 * 主函数
 */
async function main() {
  try {
    const success = await runAllTests()
    process.exit(success ? 0 : 1)
  } catch (error) {
    console.error('💥 Test execution failed:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export {
  runAllTests,
  testLinkedInAPIConnection,
  testSSEStreaming,
  testDataStructure
}
