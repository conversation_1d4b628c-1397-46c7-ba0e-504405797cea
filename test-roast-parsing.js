/**
 * 测试 roast 字段解析功能
 */

// 模拟的 roast 数据（包含 markdown 格式）
const testRoastData = `\`\`\`json
{
  "roast": "<PERSON><PERSON>: the Senior HR BP who's spent over a decade ensuring that even the plants in the office feel included! With all that talent management experience, I bet you can even convince a stapler to apply for a promotion. Just remember, in the world of HR, when you say 'inclusive environment,' you don't have to invite the photocopier to the team-building retreat!"
}
\`\`\``

// 期望的输出
const expectedOutput = "<PERSON><PERSON>: the Senior HR BP who's spent over a decade ensuring that even the plants in the office feel included! With all that talent management experience, I bet you can even convince a stapler to apply for a promotion. Just remember, in the world of HR, when you say 'inclusive environment,' you don't have to invite the photocopier to the team-building retreat!"

/**
 * 解析 roast 数据的函数（从 Vue 组件中提取）
 */
function parseRoastData(roastContent) {
  if (!roastContent) {
    return 'No roast available'
  }
  
  try {
    let content = roastContent
    
    // 首先提取 markdown 代码块中的 JSON 内容
    // 匹配 ```json ... ``` 格式
    const markdownMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
    if (markdownMatch) {
      content = markdownMatch[1].trim()
    }
    
    // 然后解析 JSON
    const roastData = JSON.parse(content)
    return roastData.roast || 'No roast available'
  } catch (e) {
    console.warn('Failed to parse roast data:', e)
    
    // 如果解析失败，尝试直接提取文本内容
    const textMatch = roastContent.match(/```json\s*{\s*"roast":\s*"([^"]*?)"\s*}\s*```/)
    if (textMatch) {
      return textMatch[1]
    }
    
    // 最后回退到原始数据
    return roastContent || 'No roast available'
  }
}

/**
 * 运行测试
 */
function runTest() {
  console.log('🧪 Testing roast parsing...\n')
  
  console.log('📥 Input:')
  console.log(testRoastData)
  console.log('\n📤 Expected Output:')
  console.log(expectedOutput)
  
  console.log('\n🔄 Parsing...')
  const result = parseRoastData(testRoastData)
  
  console.log('\n📋 Actual Output:')
  console.log(result)
  
  console.log('\n🎯 Test Result:')
  const success = result === expectedOutput
  console.log(success ? '✅ PASS - Roast parsing works correctly!' : '❌ FAIL - Roast parsing failed!')
  
  if (!success) {
    console.log('\n🔍 Debugging Info:')
    console.log('Expected length:', expectedOutput.length)
    console.log('Actual length:', result.length)
    console.log('First 100 chars of expected:', expectedOutput.substring(0, 100))
    console.log('First 100 chars of actual:', result.substring(0, 100))
  }
  
  return success
}

/**
 * 测试其他格式
 */
function testOtherFormats() {
  console.log('\n🧪 Testing other formats...\n')
  
  const testCases = [
    {
      name: 'Simple JSON without markdown',
      input: '{"roast": "Simple roast text"}',
      expected: 'Simple roast text'
    },
    {
      name: 'Plain text',
      input: 'Just plain text',
      expected: 'Just plain text'
    },
    {
      name: 'Empty string',
      input: '',
      expected: 'No roast available'
    }
  ]
  
  let allPassed = true
  
  testCases.forEach((testCase, index) => {
    console.log(`Test ${index + 1}: ${testCase.name}`)
    const result = parseRoastData(testCase.input)
    const passed = result === testCase.expected
    console.log(`${passed ? '✅' : '❌'} Expected: "${testCase.expected}"`)
    console.log(`${passed ? '✅' : '❌'} Got: "${result}"`)
    console.log('')
    
    if (!passed) allPassed = false
  })
  
  return allPassed
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 Starting roast parsing tests...\n')
  
  const mainTestPassed = runTest()
  const otherTestsPassed = testOtherFormats()
  
  const overallSuccess = mainTestPassed && otherTestsPassed
  
  console.log('📊 Final Results:')
  console.log(`Main test: ${mainTestPassed ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Other tests: ${otherTestsPassed ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Overall: ${overallSuccess ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`)
  
  return overallSuccess
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const success = main()
  process.exit(success ? 0 : 1)
}

export { parseRoastData, runTest, testOtherFormats, main }
